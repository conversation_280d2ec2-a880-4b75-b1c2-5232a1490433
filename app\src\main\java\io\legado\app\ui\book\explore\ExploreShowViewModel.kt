package io.legado.app.ui.book.explore

import android.app.Application
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import io.legado.app.BuildConfig
import io.legado.app.base.BaseViewModel
import io.legado.app.constant.AppLog
import io.legado.app.data.appDb
import io.legado.app.data.entities.BookSource
import io.legado.app.data.entities.SearchBook
import io.legado.app.help.book.isNotShelf
import io.legado.app.model.webBook.WebBook
import io.legado.app.utils.printOnDebug
import io.legado.app.utils.stackTraceStr
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.mapLatest
import java.util.concurrent.ConcurrentHashMap


@OptIn(ExperimentalCoroutinesApi::class)
class ExploreShowViewModel(application: Application) : BaseViewModel(application) {
    val bookshelf: MutableSet<String> = ConcurrentHashMap.newKeySet()
    val upAdapterLiveData = MutableLiveData<String>()
    val booksData = MutableLiveData<List<SearchBook>>()
    val errorLiveData = MutableLiveData<String>()
    val addToBookshelfLiveData = MutableLiveData<Int>()
    private var bookSource: BookSource? = null
    private var exploreUrl: String? = null
    private var page = 1
    private var books = linkedSetOf<SearchBook>()

    init {
        execute {
            appDb.bookDao.flowAll().mapLatest { books ->
                val keys = arrayListOf<String>()
                books.filterNot { it.isNotShelf }
                    .forEach {
                        keys.add("${it.name}-${it.author}")
                        keys.add(it.name)
                    }
                keys
            }.catch {
                AppLog.put("发现列表界面获取书籍数据失败\n${it.localizedMessage}", it)
            }.collect {
                bookshelf.clear()
                bookshelf.addAll(it)
                upAdapterLiveData.postValue("isInBookshelf")
            }
        }.onError {
            AppLog.put("加载书架数据失败", it)
        }
    }

    fun initData(intent: Intent) {
        execute {
            val sourceUrl = intent.getStringExtra("sourceUrl")
            exploreUrl = intent.getStringExtra("exploreUrl")
            if (bookSource == null && sourceUrl != null) {
                bookSource = appDb.bookSourceDao.getBookSource(sourceUrl)
            }
            explore()
        }
    }

    fun explore() {
        val source = bookSource
        val url = exploreUrl
        if (source == null || url == null) return
        WebBook.exploreBook(viewModelScope, source, url, page)
            .timeout(if (BuildConfig.DEBUG) 0L else 30000L)
            .onSuccess(IO) { searchBooks ->
                books.addAll(searchBooks)
                booksData.postValue(books.toList())
                appDb.searchBookDao.insert(*searchBooks.toTypedArray())
                page++
            }.onError {
                it.printOnDebug()
                errorLiveData.postValue(it.stackTraceStr)
            }
    }

    fun addAllToBookshelf(searchBooks: List<SearchBook>) {
        execute {
            var successCount = 0
            searchBooks.forEach { searchBook ->
                // 检查书籍是否已在书架中
                if (!isInBookShelf(searchBook.name, searchBook.author)) {
                    val book = searchBook.toBook()
                    // 设置书籍的顺序为当前最小值-1
                    book.order = appDb.bookDao.minOrder - 1
                    // 保存书籍到数据库
                    appDb.bookDao.insert(book)
                    successCount++
                }
            }
            successCount
        }.onSuccess {
            addToBookshelfLiveData.postValue(it)
            // 更新书架数据
            execute {
                appDb.bookDao.flowAll().mapLatest { books ->
                    val keys = arrayListOf<String>()
                    books.filterNot { it.isNotShelf }
                        .forEach {
                            keys.add("${it.name}-${it.author}")
                            keys.add(it.name)
                        }
                    keys
                }.collect {
                    bookshelf.clear()
                    bookshelf.addAll(it)
                    upAdapterLiveData.postValue("isInBookshelf")
                }
            }
        }.onError {
            AppLog.put("批量添加书籍到书架失败", it)
            addToBookshelfLiveData.postValue(0)
        }
    }

    fun isInBookShelf(name: String, author: String): Boolean {
        return if (author.isNotBlank()) {
            bookshelf.contains("$name-$author")
        } else {
            bookshelf.contains(name)
        }
    }

}
