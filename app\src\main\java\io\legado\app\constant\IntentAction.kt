package io.legado.app.constant

@Suppress("ConstPropertyName")
object IntentAction {
    const val start = "start"
    const val play = "play"
    const val playNew = "playNew"
    const val stop = "stop"
    const val resume = "resume"
    const val pause = "pause"
    const val addTimer = "addTimer"
    const val setTimer = "setTimer"
    const val prevParagraph = "prevParagraph"
    const val nextParagraph = "nextParagraph"
    const val upTtsSpeechRate = "upTtsSpeechRate"
    const val upTtsProgress = "upTtsProgress"
    const val adjustProgress = "adjustProgress"
    const val adjustSpeed = "adjustSpeed"
    const val prev = "prev"
    const val next = "next"
    const val moveTo = "moveTo"
    const val init = "init"
    const val remove = "remove"
    const val stopPlay = "stopPlay"
}