# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
registries:
  maven-google:
    type: maven-repository
    # url: https://maven.google.com
    url: https://dl.google.com/dl/android/maven2/
    password: dummy
    username: dummy
  maven-central:
    type: maven-repository
    url: https://repo1.maven.org/maven2/
    password: dummy
    username: dummy
  maven-jitpack:
    type: maven-repository
    url: https://jitpack.io
    password: dummy
    username: dummy

updates:
  - package-ecosystem: gradle
    directory: "/"
    schedule:
      interval: "weekly"
    registries: "*"
    open-pull-requests-limit: 20
    groups:
      kotlin_KSP:
        patterns:
          - "org.jetbrains.kotlin:*"
          - "com.google.devtools.ksp"
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
  - package-ecosystem: npm
    directory: "modules/web"
    schedule:
      interval: "weekly"
