import{aj as l,B as g,L as c,a8 as d,ak as f,u as m}from"./vendor-B0XMvmrm.js";const L=(s,t,u=l)=>{const a=g(!1);let r=null;const o=()=>a.value=!1,n=()=>a.value=!0;c(a,e=>{if(!e)return r==null?void 0:r.close();r=f.service({target:m(s),spinner:u,text:t,lock:!0,background:"rgba(0, 0, 0, 0)"})});const i=e=>{if(!(e instanceof Promise))throw TypeError("loadingWrapper argument must be Promise");return n(),e.finally(o)};return d(()=>{o()}),{isLoading:a,showLoading:n,closeLoading:o,loadingWrapper:i}};export{L as u};
