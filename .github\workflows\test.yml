name: Test Build

on:
  push:
    branches:
      - master
    paths:
      - '**'
      # - '!**/assets/**'
      # - '!**.md'
      - '!**/ISSUE_TEMPLATE/**'
      - '!**/modules/web/**'
  pull_request:
    paths-ignore:
      - '**/modules/web/**'
  workflow_run:
    workflows: [Build Web]
    branches: [master]
    types:
      - completed
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:

  prepare:
    runs-on: ubuntu-latest
    if: ${{ !startsWith(github.event.head_commit.message, 'Merge pull request') }}
    outputs:
      version: ${{ steps.set-ver.outputs.version }}
      versionL: ${{ steps.set-ver.outputs.versionL }}
      lanzou: ${{ steps.check.outputs.lanzou }}
      telegram: ${{ steps.check.outputs.telegram }}
    steps:
      - id: set-ver
        run: |
          echo "version=$(date -d "8 hour" -u +3.%y.%m%d%H)" >> $GITHUB_OUTPUT
          echo "versionL=$(date -d "8 hour" -u +3.%y.%m%d%H%M)" >> $GITHUB_OUTPUT
      - id: check
        run: |
          if [ ${{ secrets.LANZOU_ID }} ]; then
            echo "lanzou=yes" >> $GITHUB_OUTPUT
          fi
          if [ ${{ secrets.BOT_TOKEN }} ]; then
            echo "telegram=yes" >> $GITHUB_OUTPUT
          fi

  build:
    needs: prepare
    strategy:
      matrix:
        product: [ app ]
        type: [ release, releaseA ]
      fail-fast: false
    runs-on: ubuntu-latest
    env:
      product: ${{ matrix.product }}
      type: ${{ matrix.type }}
      VERSION: ${{ needs.prepare.outputs.version }}
      VERSIONL: ${{ needs.prepare.outputs.versionL }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 17
      - name: Clear 18PlusList.txt
        run: |
          echo "清空18PlusList.txt"
          echo "">$GITHUB_WORKSPACE/app/src/main/assets/18PlusList.txt
      - name: Release Apk Sign
        run: |
          echo "给apk增加签名"
          cp $GITHUB_WORKSPACE/.github/workflows/legado.jks $GITHUB_WORKSPACE/app/legado.jks
          sed '$a\RELEASE_STORE_FILE=./legado.jks'          $GITHUB_WORKSPACE/gradle.properties -i
          sed '$a\RELEASE_KEY_ALIAS=legado'                 $GITHUB_WORKSPACE/gradle.properties -i
          sed '$a\RELEASE_STORE_PASSWORD=gedoor_legado'     $GITHUB_WORKSPACE/gradle.properties -i
          sed '$a\RELEASE_KEY_PASSWORD=gedoor_legado'       $GITHUB_WORKSPACE/gradle.properties -i

      - name: Set up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build With Gradle
        continue-on-error: true
        run: |
          if [ ${{ env.type }} == 'release' ]; then
            typeName="原包名"
          else
            typeName="共存"
            sed "s/'.release'/'.releaseA'/" $GITHUB_WORKSPACE/app/build.gradle  -i
            sed 's/.release/.releaseA/'     $GITHUB_WORKSPACE/app/google-services.json -i
          fi
          echo "统一版本号"
          sed "/def version/c def version = \"${{ env.VERSION }}\"" $GITHUB_WORKSPACE/app/build.gradle  -i
          echo "开始${{ env.product }}$typeName构建"
          chmod +x gradlew
          ./gradlew assemble${{ env.product }}release --build-cache --parallel --daemon --warning-mode all
          echo "修改APK文件名"
          mkdir -p ${{ github.workspace }}/apk/
          for file in `ls ${{ github.workspace }}/app/build/outputs/apk/*/*/*.apk`; do
            mv "$file" ${{ github.workspace }}/apk/legado_${{ env.product }}_${{ env.VERSIONL }}_$typeName.apk
          done
          echo "移动mapping文件"
          mkdir -p ${{ github.workspace }}/mapping/
          for file in `ls ${{ github.workspace }}/app/build/outputs/mapping/*/mapping.txt`; do
            mv "$file" ${{ github.workspace }}/mapping/mapping.txt
          done

      - name: Move Missing Rules Files
        run: |
          echo "移动missing_rules.txt文件"
          mkdir -p ${{ github.workspace }}/mapping/
          for file in `ls ${{ github.workspace }}/app/build/outputs/mapping/*/missing_rules.txt`; do
            mv "$file" ${{ github.workspace }}/mapping/missing_rules.txt
          done

      - name: Upload Missing Rules File To Artifact
        uses: actions/upload-artifact@v4
        with:
          name: legado.${{ env.product }}.${{ env.type }}.mapping.missing_rules
          if-no-files-found: ignore
          path: ${{ github.workspace }}/mapping/missing_rules.txt

      - name: Check Build production
        run: |
          if [ ! -d ${{ github.workspace }}/apk ]; then
            echo "Build production not found! Check gradle logs."
            exit 1
          fi
          cd ${{ github.workspace }}/apk/
          if [ ! -e legado_*.apk ]; then
            echo "Build production not found! Check gradle logs."
            exit 1
          fi

      - name: Upload App To Artifact
        uses: actions/upload-artifact@v4
        with:
          name: legado.${{ env.product }}.${{ env.type }}
          if-no-files-found: ignore
          path: ${{ github.workspace }}/apk/*.apk

      - name: Upload Mapping File To Artifact
        uses: actions/upload-artifact@v4
        with:
          name: legado.${{ env.product }}.${{ env.type }}.mapping
          if-no-files-found: ignore
          path: ${{ github.workspace }}/mapping/mapping.txt

  prerelease:
    needs: [ prepare, build ]
    if: github.event_name != 'pull_request' && github.repository == 'gedoor/legado'
    runs-on: ubuntu-latest
    env: 
      VERSION: ${{ needs.prepare.outputs.version }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          path: apk/

      - working-directory: apk/
        run: |
          mv */*.apk .
          rm -rf */

          for file in `ls *.apk`; do
            if [[ "$file" == *原包名* ]]; then
              mv "$file" $(echo $file | sed s/原包名/release/)
            else
              mv "$file" $(echo $file | sed s/共存/releaseA/)
            fi
          done

      - name: Delete Pre-Release
        run: |
          if gh release view beta &>/dev/null; then
            gh release delete beta -y
          fi
        env:
          GH_TOKEN: ${{ github.token }}
      
      - name: Create or update beta tag
        uses: richardsimko/update-tag@v1
        with:
          tag_name: beta
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish Pre-Release
        uses: ncipollo/release-action@v1
        with:
          name: legado_app_${{ env.VERSION }}
          tag: "beta"
          body: "此版本为测试版，签名与正式版不同，可能存在不稳定情况，升级前请务必备份好数据。"
          prerelease: true
          artifacts: ${{ github.workspace }}/apk/*.apk

  lanzou:
    needs: [ prepare, build ]
    if: ${{ github.event_name != 'pull_request' && needs.prepare.outputs.lanzou == 'yes' }}
    runs-on: ubuntu-latest
    env:
      # 登录蓝奏云后在控制台运行document.cookie
      ylogin: ${{ secrets.LANZOU_ID }}
      phpdisk_info: ${{ secrets.LANZOU_PSD }}
      # 蓝奏云里的文件夹ID（阅读3测试版:2670621）
      LANZOU_FOLDER_ID: ${{ secrets.LANZOU_FOLDER_ID }}
      #蓝奏云分享链接
      LANZOU_URL: ${{ secrets.LANZOU_URL }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          path: apk/
      - working-directory: apk/
        run: mv */*.apk . ;rm -rf */
      - name: Upload To Lanzou
        continue-on-error: true
        run: |
          path="$GITHUB_WORKSPACE/apk/"
          python3 $GITHUB_WORKSPACE/.github/scripts/lzy_web.py "$path" "$LANZOU_FOLDER_ID"
          echo "[$(date -u -d '+8 hour' '+%Y.%m.%d %H:%M:%S')] 分享链接: $LANZOU_URL"

  test_Branch:
    needs: [ prepare, build ]
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'pull_request' && github.actor == 'gedoor' }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          path: apk/
      - working-directory: apk/
        run: mv */*.apk . ;rm -rf */
      - name: Push To "test" Branch
        run: |
          cd $GITHUB_WORKSPACE/apk/
          git init
          git checkout -b test
          git config --global user.name "github-actions[bot]"
          git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git remote add origin "https://${{ github.actor }}:${{ secrets.ACTIONS_TOKEN }}@github.com/${{ github.actor }}/release"
          git add *.apk
          git commit -m "${{ needs.prepare.outputs.versionL }}"
          git push -f -u origin test

  telegram:
    needs: [ prepare, build ]
    if: ${{ github.event_name != 'pull_request' && needs.prepare.outputs.telegram == 'yes' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          path: apk/
      - working-directory: apk/
        run: |
          for file in `ls */*.apk`; do
            mv "$file" "$(echo "$file"|sed -e 's#.*\/##g' -e "s/_/ /g" -e 's/legado/阅读/')"
          done
          rm -rf */
      - name: Post to channel
        uses: xireiki/channel-post@v1
        with:
          chat_id: ${{ secrets.CHANNEL_ID }}
          bot_token: ${{ secrets.BOT_TOKEN }}
          context: "#阅读 #Legado #Beta ${{ needs.prepare.outputs.versionL }}"
          path: apk/*
          method: sendFile
