.theme-vs2015-min pre code.hljs {
    display: block;
    overflow-x: auto;
    padding: 1em;
}

.theme-vs2015-min code.hljs {
    padding: 3px 5px;
}

.theme-vs2015-min .hljs {
    background: #1e1e1e;
    color: #dcdcdc;
    padding: 1rem;
    display: block;
}

.theme-vs2015-min .hljs-keyword,
.theme-vs2015-min .hljs-literal,
.theme-vs2015-min .hljs-name,
.theme-vs2015-min .hljs-symbol {
    color: #569cd6;
}

.theme-vs2015-min .hljs-link {
    color: #569cd6;
    text-decoration: underline;
}

.theme-vs2015-min .hljs-built_in,
.theme-vs2015-min .hljs-type {
    color: #4ec9b0;
}

.theme-vs2015-min .hljs-class,
.theme-vs2015-min .hljs-number {
    color: #b8d7a3;
}

.theme-vs2015-min .hljs-meta .hljs-string,
.theme-vs2015-min .hljs-string {
    color: #d69d85;
}

.theme-vs2015-min .hljs-regexp,
.theme-vs2015-min .hljs-template-tag {
    color: #9a5334;
}

.theme-vs2015-min .hljs-formula,
.theme-vs2015-min .hljs-function,
.theme-vs2015-min .hljs-params,
.theme-vs2015-min .hljs-subst,
.theme-vs2015-min .hljs-title {
    color: #dcdcdc;
}

.theme-vs2015-min .hljs-comment,
.theme-vs2015-min .hljs-quote {
    color: #57a64a;
    font-style: italic;
}

.theme-vs2015-min .hljs-doctag {
    color: #608b4e;
}

.theme-vs2015-min .hljs-meta,
.theme-vs2015-min .hljs-meta .hljs-keyword,
.theme-vs2015-min .hljs-tag {
    color: #9b9b9b;
}

.theme-vs2015-min .hljs-template-variable,
.theme-vs2015-min .hljs-variable {
    color: #bd63c5;
}

.theme-vs2015-min .hljs-attr,
.theme-vs2015-min .hljs-attribute {
    color: #9cdcfe;
}

.theme-vs2015-min .hljs-section {
    color: gold;
}

.theme-vs2015-min .hljs-emphasis {
    font-style: italic;
}

.theme-vs2015-min .hljs-strong {
    font-weight: 700;
}

.theme-vs2015-min .hljs-bullet,
.theme-vs2015-min .hljs-selector-attr,
.theme-vs2015-min .hljs-selector-class,
.theme-vs2015-min .hljs-selector-id,
.theme-vs2015-min .hljs-selector-pseudo,
.theme-vs2015-min .hljs-selector-tag {
    color: #d7ba7d;
}

.theme-vs2015-min .hljs-addition {
    background-color: #144212;
    display: inline-block;
    width: 100%;
}

.theme-vs2015-min .hljs-deletion {
    background-color: #600;
    display: inline-block;
    width: 100%;
}

pre {
    position: relative;
}

small {
    position: absolute;
    top: 0;
    right: 0;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background-color: #7777;
    border-bottom-left-radius: 0.375rem;
    color: #dcdcdc;
}
