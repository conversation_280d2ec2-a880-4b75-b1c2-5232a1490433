<!DOCTYPE HTML>
<!--
	Forty by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
-->
<html>

<head>
    <title>Legado web 导航</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
    <link rel="stylesheet" href="assets/css/main.css"/>
</head>

<body class="is-preload">

<!-- Wrapper -->
<div id="wrapper">

    <!-- Header -->
    <header id="header" class="alt">
        <nav>
            <a href="#menu">Menu</a>
        </nav>
    </header>

    <!-- Menu -->
    <nav id="menu">
        <ul class="links">
            <li><a href="https://github.com/gedoor/legado">Github</a></li>
            <li><a href="https://github.com/zsakvo">zsakvo</a></li>
            <li><a href="#">Design: HTML5 UP</a></li>
        </ul>
    </nav>

    <!-- Banner -->
    <section id="banner" class="major">
        <div class="inner">
            <header class="major">
                <h1>昨日邻家乞新火，晓窗分与读书灯</h1>
            </header>
            <div class="content">
                <!-- <p>A responsive site template designed by HTML5 UP<br />
                    and released under the Creative Commons.</p> -->
                <ul class="actions">
                    <li><a href="vue/index.html" class="button next scrolly" target="_blank">书架</a>
                    </li>
                </ul>
                <ul class="actions">
                    <li><a href="vue/index.html#/bookSource" class="button next scrolly"
                           target="_blank">书源</a></li>
                </ul>
                <ul class="actions">
                    <li><a href="uploadBook/index.html" class="button next scrolly" target="_blank">传书</a>
                    </li>
                </ul>
                <ul class="actions">
                    <li><a href="vue/index.html#/rssSource" class="button next scrolly"
                           target="_blank">订阅源</a></li>
                </ul>
            </div>
        </div>
    </section>
</div>

<!-- Scripts -->
<script src="assets/js/dist.js"></script>

</body>

</html>