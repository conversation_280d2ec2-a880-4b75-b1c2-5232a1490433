{"formatVersion": 1, "database": {"version": 27, "identityHash": "e20aa63032efb23c9e9c269afd64e7d7", "entities": [{"tableName": "books", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bookUrl` TEXT NOT NULL, `tocUrl` TEXT NOT NULL, `origin` TEXT NOT NULL, `originName` TEXT NOT NULL, `name` TEXT NOT NULL, `author` TEXT NOT NULL, `kind` TEXT, `customTag` TEXT, `coverUrl` TEXT, `customCoverUrl` TEXT, `intro` TEXT, `customIntro` TEXT, `charset` TEXT, `type` INTEGER NOT NULL, `group` INTEGER NOT NULL, `latestChapterTitle` TEXT, `latestChapterTime` INTEGER NOT NULL, `lastCheckTime` INTEGER NOT NULL, `lastCheckCount` INTEGER NOT NULL, `totalChapterNum` INTEGER NOT NULL, `durChapterTitle` TEXT, `durChapterIndex` INTEGER NOT NULL, `durChapterPos` INTEGER NOT NULL, `durChapterTime` INTEGER NOT NULL, `wordCount` TEXT, `canUpdate` INTEGER NOT NULL, `order` INTEGER NOT NULL, `originOrder` INTEGER NOT NULL, `variable` TEXT, `readConfig` TEXT, PRIMARY KEY(`bookUrl`))", "fields": [{"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tocUrl", "columnName": "tocUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originName", "columnName": "originName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "author", "columnName": "author", "affinity": "TEXT", "notNull": true}, {"fieldPath": "kind", "columnName": "kind", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customTag", "columnName": "customTag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverUrl", "columnName": "coverUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customCoverUrl", "columnName": "customCoverUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "intro", "columnName": "intro", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customIntro", "columnName": "customIntro", "affinity": "TEXT", "notNull": false}, {"fieldPath": "charset", "columnName": "charset", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "group", "columnName": "group", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latestChapterTitle", "columnName": "latestChapterTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "latestChapterTime", "columnName": "latestChapterTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastCheckTime", "columnName": "lastCheckTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastCheckCount", "columnName": "lastCheckCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalChapterNum", "columnName": "totalChapterNum", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dur<PERSON>hapterTitle", "columnName": "dur<PERSON>hapterTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "durChapterIndex", "columnName": "durChapterIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "durChapterPos", "columnName": "durChapterPos", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "durChapterTime", "columnName": "durChapterTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wordCount", "columnName": "wordCount", "affinity": "TEXT", "notNull": false}, {"fieldPath": "canUpdate", "columnName": "canUpdate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "originOrder", "columnName": "originOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}, {"fieldPath": "readConfig", "columnName": "readConfig", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["bookUrl"], "autoGenerate": false}, "indices": [{"name": "index_books_name_author", "unique": true, "columnNames": ["name", "author"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_books_name_author` ON `${TABLE_NAME}` (`name`, `author`)"}], "foreignKeys": []}, {"tableName": "book_groups", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `groupName` TEXT NOT NULL, `order` INTEGER NOT NULL, `show` INTEGER NOT NULL, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupName", "columnName": "groupName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "show", "columnName": "show", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["groupId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "book_sources", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bookSourceName` TEXT NOT NULL, `bookSourceGroup` TEXT, `bookSourceUrl` TEXT NOT NULL, `bookSourceType` INTEGER NOT NULL, `bookUrlPattern` TEXT, `customOrder` INTEGER NOT NULL, `enabled` INTEGER NOT NULL, `enabledExplore` INTEGER NOT NULL, `header` TEXT, `loginUrl` TEXT, `bookSourceComment` TEXT, `lastUpdateTime` INTEGER NOT NULL, `weight` INTEGER NOT NULL, `exploreUrl` TEXT, `ruleExplore` TEXT, `searchUrl` TEXT, `ruleSearch` TEXT, `ruleBookInfo` TEXT, `ruleToc` TEXT, `ruleContent` TEXT, PRIMARY KEY(`bookSourceUrl`))", "fields": [{"fieldPath": "bookSourceName", "columnName": "bookSourceName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookSourceGroup", "columnName": "bookSourceGroup", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bookSourceUrl", "columnName": "bookSourceUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookSourceType", "columnName": "bookSourceType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookUrlPattern", "columnName": "bookUrlPattern", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customOrder", "columnName": "customOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enabled", "columnName": "enabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enabledExplore", "columnName": "enabledExplore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "header", "columnName": "header", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginUrl", "columnName": "loginUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bookSourceComment", "columnName": "bookSourceComment", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weight", "columnName": "weight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "exploreUrl", "columnName": "exploreUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleExplore", "columnName": "ruleExplore", "affinity": "TEXT", "notNull": false}, {"fieldPath": "searchUrl", "columnName": "searchUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleSearch", "columnName": "ruleSearch", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleBookInfo", "columnName": "ruleBookInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleToc", "columnName": "ruleToc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleContent", "columnName": "ruleContent", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["bookSourceUrl"], "autoGenerate": false}, "indices": [{"name": "index_book_sources_bookSourceUrl", "unique": false, "columnNames": ["bookSourceUrl"], "createSql": "CREATE INDEX IF NOT EXISTS `index_book_sources_bookSourceUrl` ON `${TABLE_NAME}` (`bookSourceUrl`)"}], "foreignKeys": []}, {"tableName": "chapters", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`url` TEXT NOT NULL, `title` TEXT NOT NULL, `baseUrl` TEXT NOT NULL, `bookUrl` TEXT NOT NULL, `index` INTEGER NOT NULL, `resourceUrl` TEXT, `tag` TEXT, `start` INTEGER, `end` INTEGER, `variable` TEXT, PRIMARY KEY(`url`, `bookUrl`), FOREIGN KEY(`bookUrl`) REFERENCES `books`(`bookUrl`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "baseUrl", "columnName": "baseUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "index", "columnName": "index", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "resourceUrl", "columnName": "resourceUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "start", "columnName": "start", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "end", "columnName": "end", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["url", "bookUrl"], "autoGenerate": false}, "indices": [{"name": "index_chapters_bookUrl", "unique": false, "columnNames": ["bookUrl"], "createSql": "CREATE INDEX IF NOT EXISTS `index_chapters_bookUrl` ON `${TABLE_NAME}` (`bookUrl`)"}, {"name": "index_chapters_bookUrl_index", "unique": true, "columnNames": ["bookUrl", "index"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_chapters_bookUrl_index` ON `${TABLE_NAME}` (`bookUrl`, `index`)"}], "foreignKeys": [{"table": "books", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["bookUrl"], "referencedColumns": ["bookUrl"]}]}, {"tableName": "replace_rules", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `group` TEXT, `pattern` TEXT NOT NULL, `replacement` TEXT NOT NULL, `scope` TEXT, `isEnabled` INTEGER NOT NULL, `isRegex` INTEGER NOT NULL, `sortOrder` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "group", "columnName": "group", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pattern", "columnName": "pattern", "affinity": "TEXT", "notNull": true}, {"fieldPath": "replacement", "columnName": "replacement", "affinity": "TEXT", "notNull": true}, {"fieldPath": "scope", "columnName": "scope", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isEnabled", "columnName": "isEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isRegex", "columnName": "isRegex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "order", "columnName": "sortOrder", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_replace_rules_id", "unique": false, "columnNames": ["id"], "createSql": "CREATE INDEX IF NOT EXISTS `index_replace_rules_id` ON `${TABLE_NAME}` (`id`)"}], "foreignKeys": []}, {"tableName": "searchBooks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bookUrl` TEXT NOT NULL, `origin` TEXT NOT NULL, `originName` TEXT NOT NULL, `type` INTEGER NOT NULL, `name` TEXT NOT NULL, `author` TEXT NOT NULL, `kind` TEXT, `coverUrl` TEXT, `intro` TEXT, `wordCount` TEXT, `latestChapterTitle` TEXT, `tocUrl` TEXT NOT NULL, `time` INTEGER NOT NULL, `variable` TEXT, `originOrder` INTEGER NOT NULL, PRIMARY KEY(`bookUrl`), FOREIGN KEY(`origin`) REFERENCES `book_sources`(`bookSourceUrl`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originName", "columnName": "originName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "author", "columnName": "author", "affinity": "TEXT", "notNull": true}, {"fieldPath": "kind", "columnName": "kind", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverUrl", "columnName": "coverUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "intro", "columnName": "intro", "affinity": "TEXT", "notNull": false}, {"fieldPath": "wordCount", "columnName": "wordCount", "affinity": "TEXT", "notNull": false}, {"fieldPath": "latestChapterTitle", "columnName": "latestChapterTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tocUrl", "columnName": "tocUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}, {"fieldPath": "originOrder", "columnName": "originOrder", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["bookUrl"], "autoGenerate": false}, "indices": [{"name": "index_searchBooks_bookUrl", "unique": true, "columnNames": ["bookUrl"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_searchBooks_bookUrl` ON `${TABLE_NAME}` (`bookUrl`)"}, {"name": "index_searchBooks_origin", "unique": false, "columnNames": ["origin"], "createSql": "CREATE INDEX IF NOT EXISTS `index_searchBooks_origin` ON `${TABLE_NAME}` (`origin`)"}], "foreignKeys": [{"table": "book_sources", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["origin"], "referencedColumns": ["bookSourceUrl"]}]}, {"tableName": "search_keywords", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`word` TEXT NOT NULL, `usage` INTEGER NOT NULL, `lastUseTime` INTEGER NOT NULL, PRIMARY KEY(`word`))", "fields": [{"fieldPath": "word", "columnName": "word", "affinity": "TEXT", "notNull": true}, {"fieldPath": "usage", "columnName": "usage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUseTime", "columnName": "lastUseTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["word"], "autoGenerate": false}, "indices": [{"name": "index_search_keywords_word", "unique": true, "columnNames": ["word"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_search_keywords_word` ON `${TABLE_NAME}` (`word`)"}], "foreignKeys": []}, {"tableName": "cookies", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`url` TEXT NOT NULL, `cookie` TEXT NOT NULL, PRIMARY KEY(`url`))", "fields": [{"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "cookie", "columnName": "cookie", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["url"], "autoGenerate": false}, "indices": [{"name": "index_cookies_url", "unique": true, "columnNames": ["url"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_cookies_url` ON `${TABLE_NAME}` (`url`)"}], "foreignKeys": []}, {"tableName": "rssSources", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sourceUrl` TEXT NOT NULL, `sourceName` TEXT NOT NULL, `sourceIcon` TEXT NOT NULL, `sourceGroup` TEXT, `enabled` INTEGER NOT NULL, `sortUrl` TEXT, `singleUrl` INTEGER NOT NULL, `articleStyle` INTEGER NOT NULL, `ruleArticles` TEXT, `ruleNextPage` TEXT, `ruleTitle` TEXT, `rulePubDate` TEXT, `ruleDescription` TEXT, `ruleImage` TEXT, `ruleLink` TEXT, `ruleContent` TEXT, `style` TEXT, `header` TEXT, `enableJs` INTEGER NOT NULL, `loadWithBaseUrl` INTEGER NOT NULL, `customOrder` INTEGER NOT NULL, PRIMARY KEY(`sourceUrl`))", "fields": [{"fieldPath": "sourceUrl", "columnName": "sourceUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceName", "columnName": "sourceName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceIcon", "columnName": "sourceIcon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceGroup", "columnName": "sourceGroup", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enabled", "columnName": "enabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sortUrl", "columnName": "sortUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "singleUrl", "columnName": "singleUrl", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "articleStyle", "columnName": "articleStyle", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ruleArticles", "columnName": "ruleArticles", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleNextPage", "columnName": "ruleNextPage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleTitle", "columnName": "ruleTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "rulePubDate", "columnName": "rulePubDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleDescription", "columnName": "ruleDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleImage", "columnName": "ruleImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleLink", "columnName": "ruleLink", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleContent", "columnName": "ruleContent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "style", "columnName": "style", "affinity": "TEXT", "notNull": false}, {"fieldPath": "header", "columnName": "header", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enableJs", "columnName": "enableJs", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "loadWithBaseUrl", "columnName": "loadWithBaseUrl", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "customOrder", "columnName": "customOrder", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["sourceUrl"], "autoGenerate": false}, "indices": [{"name": "index_rssSources_sourceUrl", "unique": false, "columnNames": ["sourceUrl"], "createSql": "CREATE INDEX IF NOT EXISTS `index_rssSources_sourceUrl` ON `${TABLE_NAME}` (`sourceUrl`)"}], "foreignKeys": []}, {"tableName": "bookmarks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`time` INTEGER NOT NULL, `bookUrl` TEXT NOT NULL, `bookName` TEXT NOT NULL, `bookAuthor` TEXT NOT NULL, `chapterIndex` INTEGER NOT NULL, `chapterPos` INTEGER NOT NULL, `chapterName` TEXT NOT NULL, `bookText` TEXT NOT NULL, `content` TEXT NOT NULL, PRIMARY KEY(`time`))", "fields": [{"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookName", "columnName": "bookName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "book<PERSON><PERSON><PERSON>", "columnName": "book<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chapterIndex", "columnName": "chapterIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chapterPos", "columnName": "chapterPos", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chapterName", "columnName": "chapterName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookText", "columnName": "bookText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["time"], "autoGenerate": false}, "indices": [{"name": "index_bookmarks_time", "unique": true, "columnNames": ["time"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_bookmarks_time` ON `${TABLE_NAME}` (`time`)"}], "foreignKeys": []}, {"tableName": "rssArticles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`origin` TEXT NOT NULL, `sort` TEXT NOT NULL, `title` TEXT NOT NULL, `order` INTEGER NOT NULL, `link` TEXT NOT NULL, `pubDate` TEXT, `description` TEXT, `content` TEXT, `image` TEXT, `read` INTEGER NOT NULL, PRIMARY KEY(`origin`, `link`))", "fields": [{"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "link", "columnName": "link", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pubDate", "columnName": "pubDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": false}, {"fieldPath": "read", "columnName": "read", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["origin", "link"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "rssReadRecords", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`record` TEXT NOT NULL, `read` INTEGER NOT NULL, PRIMARY KEY(`record`))", "fields": [{"fieldPath": "record", "columnName": "record", "affinity": "TEXT", "notNull": true}, {"fieldPath": "read", "columnName": "read", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["record"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "rssStars", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`origin` TEXT NOT NULL, `sort` TEXT NOT NULL, `title` TEXT NOT NULL, `starTime` INTEGER NOT NULL, `link` TEXT NOT NULL, `pubDate` TEXT, `description` TEXT, `content` TEXT, `image` TEXT, PRIMARY KEY(`origin`, `link`))", "fields": [{"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "starTime", "columnName": "starTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "link", "columnName": "link", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pubDate", "columnName": "pubDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["origin", "link"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "txtTocRules", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `rule` TEXT NOT NULL, `serialNumber` INTEGER NOT NULL, `enable` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rule", "columnName": "rule", "affinity": "TEXT", "notNull": true}, {"fieldPath": "serialNumber", "columnName": "serialNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enable", "columnName": "enable", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "readRecord", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`androidId` TEXT NOT NULL, `bookName` TEXT NOT NULL, `readTime` INTEGER NOT NULL, PRIMARY KEY(`androidId`, `bookName`))", "fields": [{"fieldPath": "androidId", "columnName": "androidId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookName", "columnName": "bookName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "readTime", "columnName": "readTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["androidId", "bookName"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "httpTTS", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `url` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "caches", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `value` TEXT, `deadline` INTEGER NOT NULL, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deadline", "columnName": "deadline", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["key"], "autoGenerate": false}, "indices": [{"name": "index_caches_key", "unique": true, "columnNames": ["key"], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_caches_key` ON `${TABLE_NAME}` (`key`)"}], "foreignKeys": []}, {"tableName": "ruleSubs", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `url` TEXT NOT NULL, `type` INTEGER NOT NULL, `customOrder` INTEGER NOT NULL, `autoUpdate` INTEGER NOT NULL, `update` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "customOrder", "columnName": "customOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "autoUpdate", "columnName": "autoUpdate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "update", "columnName": "update", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e20aa63032efb23c9e9c269afd64e7d7')"]}}