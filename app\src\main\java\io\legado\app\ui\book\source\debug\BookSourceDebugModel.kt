package io.legado.app.ui.book.source.debug

import android.app.Application
import io.legado.app.base.BaseViewModel
import io.legado.app.data.appDb
import io.legado.app.data.entities.BookSource
import io.legado.app.model.Debug

class BookSourceDebugModel(application: Application) : BaseViewModel(application),
    Debug.Callback {

    var bookSource: BookSource? = null
    private var callback: ((Int, String) -> Unit)? = null
    var searchSrc: String? = null
    var bookSrc: String? = null
    var tocSrc: String? = null
    var contentSrc: String? = null
    var debugBook: Book? = null  // 存储调试过程中获取的书籍信息
    private var debugSuccessCallback: (() -> Unit)? = null  // 调试成功回调

    fun init(sourceUrl: String?, finally: () -> Unit) {
        sourceUrl?.let {
            //优先使用这个，不会抛出异常
            execute {
                bookSource = appDb.bookSourceDao.getBookSource(sourceUrl)
            }.onFinally {
                finally.invoke()
            }
        }
    }

    fun observe(callback: (Int, String) -> Unit) {
        this.callback = callback
    }

    fun observeDebugSuccess(callback: () -> Unit) {
        this.debugSuccessCallback = callback
    }

    fun startDebug(key: String, start: (() -> Unit)? = null, error: (() -> Unit)? = null) {
        execute {
            Debug.callback = this@BookSourceDebugModel
            Debug.startDebug(this, bookSource!!, key)
        }.onStart {
            start?.invoke()
        }.onError {
            error?.invoke()
        }
    }

    override fun printLog(state: Int, msg: String) {
        when (state) {
            10 -> searchSrc = msg
            20 -> bookSrc = msg
            30 -> tocSrc = msg
            40 -> contentSrc = msg
            1000 -> {
                // 调试成功，触发回调
                callback?.invoke(state, msg)
                debugSuccessCallback?.invoke()
            }
            else -> callback?.invoke(state, msg)
        }
    }

    fun addToBookshelf(success: (() -> Unit)? = null) {
        Debug.debugBook?.let { book ->
            execute {
                // 检查书籍是否已在书架中
                val existingBook = appDb.bookDao.getBook(book.name, book.author)
                if (existingBook != null) {
                    // 书籍已存在，更新信息
                    book.durChapterIndex = existingBook.durChapterIndex
                    book.durChapterPos = existingBook.durChapterPos
                    book.durChapterTitle = existingBook.durChapterTitle
                } else {
                    // 新书籍，设置排序
                    book.order = appDb.bookDao.minOrder - 1
                }
                book.save()
            }.onSuccess {
                success?.invoke()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        Debug.cancelDebug(true)
    }

}
