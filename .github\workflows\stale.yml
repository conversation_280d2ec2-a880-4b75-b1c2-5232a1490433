# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: closeStaleIssue

on:
  schedule:
  # 每5天北京时间9点
  - cron: '30 1 1/5 * *'
  workflow_dispatch:

jobs:
  stale:

    runs-on: ubuntu-latest
    if: ${{ github.repository == 'gedoor/legado' }}
    permissions:
      issues: write

    steps:
    - uses: actions/stale@v9
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: '由于长期没有状态更新，该问题将于5天后自动关闭。如有需要可重新打开。'
        days-before-stale: 30
        days-before-close: 5
        operations-per-run: 100
