import{d as Ae,a2 as Ge,s as l,B as U,S as Pe,a8 as Le,e as m,o as f,F as ee,g as t,y as M,P as re,u as i,a9 as J,aa as Me,L as Se,x as D,M as fe,f as K,ab as qe,w as R,ac as Ze,ad as Ye,G as me,ae as Re,z as je,h as T,a7 as Ke,n as L,af as Xe,a5 as ze,ag as _e,V as $e,O as Ve,a6 as et,ah as tt,ai as ot,c as nt}from"./vendor-B0XMvmrm.js";import{u as le,i as st,A as ce,_ as de,c as Fe}from"./index-DTncJNuO.js";import{u as it}from"./loading-ByWmlKy3.js";const at=(d,s,u,B)=>(d/=B/2,d<1?u/2*d*d+s:(d--,-u/2*(d*(d-2)-1)+s)),rt=()=>{let d,s,u,B,n,I,p,w,k,V,x,E,y;function Q(){let g=d.scrollTop||d.scrollY||d.pageYOffset;return g=typeof g>"u"?0:g,g}function h(g){const b=g.getBoundingClientRect().top,te=d.getBoundingClientRect?d.getBoundingClientRect().top:0;return b-te+u}function C(g){d.scrollTo?d.scrollTo(0,g):d.scrollTop=g}function a(g){V||(V=g),x=g-V,E=I(x,u,w,k),C(E),x<k?requestAnimationFrame(a):c()}function c(){C(u+w),s&&p&&(s.setAttribute("tabindex","-1"),s.focus()),typeof y=="function"&&y(),V=!1}function A(g,b={}){switch(k=b.duration||1e3,n=b.offset||0,y=b.callback,I=b.easing||at,p=b.a11y||!1,typeof b.container){case"object":d=b.container;break;case"string":d=document.querySelector(b.container);break;default:d=window}switch(u=Q(),typeof g){case"number":s=void 0,p=!1,B=u+g;break;case"object":s=g,B=h(s);break;case"string":s=document.querySelector(g),B=h(s);break}switch(w=B-u+n,typeof b.duration){case"number":k=b.duration;break;case"function":k=b.duration(w);break}requestAnimationFrame(a)}return A},_=rt(),lt=["data-chapterpos"],ct=["src"],At=["innerHTML"],dt=Ae({__name:"ChapterContent",props:{chapterIndex:{},contents:{},title:{},spacing:{},fontFamily:{},fontSize:{}},emits:["readedLengthChange"],setup(d,{expose:s,emit:u}){Ge(a=>({"28ad97fb":p.spacing.letter,"3d941509":p.spacing.line,"2e896d0e":p.spacing.paragraph}));const B=le(),n=l(()=>B.config.readWidth),I=l(()=>B.readingBook.bookUrl),p=d,w=a=>{const c=/<img[^>]*src="([^"]*(?:"[^>]+\})?)"[^>]*>/,A=a.match(c)[1];return st(A)?ce.getProxyImageUrl(I.value,A,le().config.readWidth):A},k=a=>{a.target.src=ce.getProxyImageUrl(I.value,a.target.src,n.value)},V=a=>{const c=/<img[^>]*src="[^"]*(?:"[^>]+\})?"[^>]*>/g;return a.replace(c," ").length},x=l(()=>{let a=-1;return Array.from(p.contents,c=>(a+=V(c)+1,a))}),E=U(),y=U();s({scrollToReadedLength:a=>{if(a===0)return;const c=x.value.findIndex(A=>A>=a);c!==-1&&Me(()=>{_(y.value[c],{duration:0})})}});let h=null;const C=u;return Pe(()=>{h=new IntersectionObserver(a=>{for(const{target:c,isIntersecting:A}of a)A&&C("readedLengthChange",p.chapterIndex,parseInt(c.dataset.chapterpos))},{rootMargin:`0px 0px -${window.innerHeight-24}px 0px`}),h.observe(E.value),y.value.forEach(a=>{h.observe(a)})}),Le(()=>{h==null||h.disconnect(),h=null}),(a,c)=>(f(),m(ee,null,[t("div",{class:"title","data-chapterpos":"0",ref_key:"titleRef",ref:E},M(a.title),513),(f(!0),m(ee,null,re(a.contents,(A,g)=>(f(),m("div",{key:g,ref_for:!0,ref_key:"paragraphRef",ref:y,"data-chapterpos":i(x)[g]},[/^\s*<img[^>]*src[^>]+>$/.test(String(A))?(f(),m("img",{key:0,class:"full",src:w(A),onErrorOnce:k,loading:"lazy"},null,40,ct)):(f(),m("p",{key:1,style:J({fontFamily:a.fontFamily,fontSize:a.fontSize}),innerHTML:A},null,12,At))],8,lt))),128))],64))}}),ut=de(dt,[["__scopeId","data-v-fb377b8c"]]),gt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXr5djn4dTp49bt59rT6LKxAAACnElEQVQozw3NUUwScRzA8d8R6MF8YMIx8uk47hDSJbj14IPzOGc7jPLvwTGg5uAYDbe2tt56cLtznvEnS6yDqCcEaWi91DvrbLJZz7b1aFtz1aO+2OZWvn+/+4CHeB6BMYaqBLfjPNRY6RFT2JJYby+uAk4WUTrtlmJ4hgPYb2q1XGDQjaK8pgJHvqNaAX+KyuIkDXpgQinb46nOulnn4b5laUHTxLfseeArAoNOeJlOIjdoal0n1FA7tKFv5roK+YaHOqP3P0XyKHPHY+MhTRe5uCZnKhtJKw2eSrSoBDPLtpZuNcFNJcFyiCMxOaaHIfXz1e8HQbWLySrBQ4x0x1qlhnHlnz2HQEC6TNb0gTHXa7IKhcaHqkE015hk9whA0YeWiLIXf7Fa2CZo3DjqjB4tTuF8jIcbfcEx5z/w4sXpQhXW+ju0cqh7icTFmRMaG+v6CIvTjcSpHcH8JEsF3EPh3fRthYdVLLgI2fWXm85/pGFE4l046s70L+yKCcirGFR+jbpy3kMmiCGHrSezVONsn1RBixncyk2PcVWk7DlgxHo8iZwDyq5uAUD854dZhdIFYzKoQig2haUKi1lVufz2RZUZPZ41n/hrOQB6h0Hhg8I367FNoEHgeM/KY7szSeQwD8q2WE3HM35ZLl0K1MJiOtHIkBclRQUwZnyOWcNsRQQgVLj1PSqkjF9DsoOSaSg3iinKzvfmgsNFFfpP/2T3GLGvL4fHEfwIX1sVvXcPqLztehWGcfn9nI2U9nTfCgJPe/jFPLZwgVEzimBgAm0VIyK2tt1cE/AzQdLK+SxLSQ4aDCZnnId94OG2S1XwvnTbNk/ZnhyRCQT+sZM6z9g6LXL1BOBe+zJySiFkHAINCtnQokbCJ/apCv0foqPiZVfhpywAAAAASUVORK5CYII=",pt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAACVBMVEX28ef48+n69esoK7jYAAAB4UlEQVQozw2OsW4bQQxEhwLXkDrysGdEqRRgVShfQQq8wOr2jD0jSpXCLvwXbtKfADlFqgSwC/9ljqweZgYzQFnb/QGepYhA9jzmTc1WaSEtQpbFgjWATI00ZZtIckXx8q2Oe5yEByBy+RHOTcM+VVTadULsvxvRC/q8WTwgcWGD+Mnaqa0oy2gw2pKFzK+PzEsus5hP9AHojKslVynLlioVTBEN8cjDNnZoR1uMGTiZAAN47HxMtEkGUE9b8HWzkqNX5Lpk0yVziAJOs46rK1pG/xNuXLjz95fSDoJE5IqG23MAYPtWoeWPvfVtIV/Ng9oH3W0gGMPIOqd4MK4QZ55dV61gOb8Zxp7I9qayaGxp6Q91cmC0ZRdBwEQVHWzSAanlZwVWc9yljeTCeaHjBVvlPSLeyeBUT2rPdJegQI103jVS3uYkyIx1il6mslMDedZuOkwzolsagvPuQAfp7cYg7k9V1NOxfq64PNSvMdwONV4VYEmqlbpZy5OAakRKkjPnL4CBv5/OZRgoWHBmNbxB0LgB1I4vXFj93UoF2/0TPEsWwV9EhbIiTPqYoTHYoMn3enTDjmrFeDTIzaL1bUC/PBIMuF+vSSYSaxoVt90EO3Gu1zrMuMRGUk7Ffv3L+A931Gsb/yBoIgAAAABJRU5ErkJggg==",ft="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEX6+fP8+/X+/ff///kbczPAAAACeElEQVQozxXHQUgUUQAG4P8936yzs6VvZNZmN9QxVxiF9OLBoOjtOC6rQq6ygXjI2fCQBdXBg4egtzFGdqkoI+zgBFbqkm3hQSxhFYLotOcubeKhOnVYoqQy+m4f5g5TvpX0xHLbLY9j8SMhJp+Jk4LfAUS2kVRIjILmnwGBTX42PhCVlDJQkIiy2nWAvaJ1h+oFIpJ0hMSYVbyyrgDWshcMpMyL1brPDQKWmduO+KTJ6XeXAMK9Yc3FpD7atyNwg6kt5XgFpLPhjUTFSYVn2abDiugGShwD8JTVRJVo/2ecuKtRb/qc4BK+9TboFfokog4T2Fn6Oqdnsjk90NMS76Rji6E0NmwkPBAZ4Xbkw8KoDAkAbEhkc78e9omxxgxg6qa5HvMv+UZbCV0qmHnSHKl5TxeA2XTCGWekR581mwC5crBH81PznASqB9va3TbkYAjJPLfg5uBfXaJgIgIBv9eessRIhxe7PA7kj6uUMeMaQ/OEQOYRaaHlqH2Gxwsl6E/pwVY5FH7uCypBZPKvDQyVziYBrAkMURe2MOOOxG/eQpp5PF+bFzUV5HtPj9GeiVSNZDELleifYTp9NAjsoiXg4cW+4ZORkdSMB/B74aAdjhsVakhgkugsbmqcDSLEoWp8zRjrux3tli6Q5uM3E+maT99Wy0RiP7tboiuRZle2c6CYeL2kcUc1KvPtQKucogMadKVTQOJYCeyCYlhQQ/Q7Etfd/vBygy9iqy+LyHeF46saCYvW6ingsbA9RBWtdi8GgUXW+oQx9/wP6bAAX1TWeV+CbShZDlQ9xT6SoSxZmKRAkmXb60kzEzkRF+Ccb94BGspGJoN/UzmyR4wjXHAAAAAASUVORK5CYII=",mt="data:image/png;base64,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",vt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAALVBMVEXx58b168ny6Mjz6sn06sf27Mvw5sTz6cbw5cLy58T37svv47/168v37s7t4Ltrv0//AAAEjUlEQVQ4yw2Ty2sTURxGf3dmOqmPxb0zmaStCnfmZpL6gpmbxIpUSMZGrSJkxsZiVZimNVaqMklrUnWTRq2KIDFWWx+IFrIRFxXEB4KIgqu6EBdu7M6FIPg32PW3+DhwDmBaYrK56KP4HGIsvg/uvOV0wK+qgBMlO9BujuH4DSJlOseqV5a/BEF97gt0ChyIPqBhXI9BtqtIB8vJB/LdCQ3OVjaLNX0g7+OmoI4e7nkemAqX6o8vg0yyQAyQS7IfgvFbI+6QyI3R4KELxw7kwM2ooQfyQigYnwY5MZbMlHI1DvnQVCoVcrt+R+bO7vPDif3ybNajwqAAe443dpfDsPt379VMWZzGRuqM79mQF+DUz9nt74bQ8J/O80MtVR51U02JKKmTCvTzLVf+vuxP/aHnPo9+2bW+zVsJ0Y630/CrfzX+b+UL+7O68Rczv+7lrMh5etfKXvhc2rk6KforxuoO2xB2tcxKfeXHt18rHOiHI/0RRjW/YGRDkHiwo3nzqL60o58C/bgRuaj7vk+QOwOhpnFNdjuWpKMCGP8Yapu9Ty5FTHKQLGSEFikjd9ADwP9ciaNNjc5qMH6w50AF/LKOsOYqsOG9GjKgc7ZXolqntm6fysJ6Ma6ll2CiqmOgE6O7x1wXExklbeqMYcwsmJmOoigt8SBg2WfilDSsAZJcBxDcrqtBXzFQJqZNHfscyIhoZlygAtyYAceah+elrFbI+46gEHDGiW878Kj7JpWyfhg6iyRMymV1MKBSeVpfgLHIohyTojI6sRyK1VpcqzVZeEBLOnA9unhGKUXPJDYtV9Dxuz4iA5xSkSWhCJdAiJR9PHlvfvbntbrR14FDqUNRAYDJmSnv3oKxuz5+7fiblgVJyYLTbgUM05P7LESkoXvyWNfb0aUU6FZizgQIa25VqKQZqFrk6v6BsqqIHlQmkQ9KrBhkC20/DrFsAFEEYLjM+lj2wYHXCwnNvZQR42XJ2iVK+UBXnI+OBE6oXpUUHiQ1yg0MhA03iwGbnOdQYc1CMiPIPQrCQJFH4L4BMFktAtKd9PN5gnU2Gra4KuK+V+mjtBRpAGIqDVe4wnSnajiFGO5d7smvhVQEMEYwqshrENIEaY7YeblJYtsb3QhAHWZCEKK67swwPMKw0If1Ta+6DgHmlgPzcUTSbi3rrv1Y64/BYEMPQ5SDHUOR022B4QRF6xLUPAaPX/V4IDI5N2BMwx4LqO1uO4j6uW7NvM7lATqGAxY/ZHVgoGZbu7SvkNR75x6qGSB23FdouENVwN7sCbewTdsXGrrnQ5ZZKOCOFtMTIzxlPu6eYmtL+nMFmoK7OeXajn86r9sqWbfmvHC4IagE5qfCPGZvLSq5F55hHIxJFa4/vRxHBlz0og4TojU1l/MOHJX17lybdF0mQhFO44JYUNt3UA473IXw/iPfDWtKG5oFSXIF5iU/VnyDSjxxeDk3jAXRyVyGTNB9FxH9qcFDNJpVbt2y9LytUXkK7Py6+z1RezHQqnoY8XcLimmd8dCnBhQCuaGpJCq3SoIlmYvLz8UkWhJw7T8k+Db/DYEKwgAAAABJRU5ErkJggg==",Bt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAD1BMVEX48dr48Nf58tv379X17NJtIBxUAAACFUlEQVQ4y1XRUZakMAgF0Af2AiDWApDZgHZqAV1nZv9rGh7Rj7Y8McUFEg1wvcMESMNVD/neU8Xcaz7nYYkYlYO6Ti82PBI4BvIEg1aj3wKwRvIMgZsUy5LdhCawPFh1sZs4SrlyN9fQKpv8s5dgZ2eLyqqJiu+WkCmUEybXkm3INS01WAiv0PapJ0CZc0SJQUzcWnZYbOOY20iFD8Bk+/j2A3wNxH7GdShFYS5ff237kXh9I9zSkQmIAhOsOSVfJ6DIXTMDaPnzkRJ92S1BQQmXl5LdirgRLLDdcYqcGPwe3QN4xCBiGNbrqq9wpW1XCecChwaQdVOsRDpPCpeoolPdxeXp3WNB9PHVzWBHlygy4NJCCrFHREv6bDt0VGwJZASkpONmm1UseGeFKAQexgaAkrfYWl3AGxWOLL2AIMBNbCXpktmS3k3vHeYjGCPBa43wJTurO3ZFVpQSJdAZGLoHTyk1upkjxMEaIxum3iIARcCa5kSkFAW5fi1mUlL9eyOsaanFmOMruwvEdE3ZYzsRSzo5ewRLXyVPPEvknt8ij4DvCg2O7xOgBCUprEzV4z1WekSpUgI8DT2mrnSOXKRfQavwuKA1F+tFnMKdJSUpMA7wQAifWRkMgjUKKZE4lBl6MCM4B1pq1P4uIjDE6Pq6rL0FnW1nIFmta5vrSvq/Ch4tpqG/ZNyyWa5jZPktq81eYv8Bt5s4iFITOp4AAAAASUVORK5CYII=",kt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXN383Q4tDP4c/R5NEInCCXAAACVElEQVQozw3Hv2sTYRwH4M/79pJ7bZL2bXqtERJ97zjUpbZDhg6pfC8qibi8hLR0EaJ0EFxaCSWDxjfpj1zrYBcRBKE6SAfBJWsx9i8IQfdQxDlKtA6t2OnhQfN3lbG7ytYRywF8rVoPCNO0X2sQOKDpAnSDK2VwkHgmh5yLGT8qASt+2KofnNt2Xg1gf1UF8AoM6052cRMNaloLZb7RKQGrKKji2OefsZF+VqIvos5ZLVIZCX61JcwUdk56wASVkgQvzPfvmT2twTSwyYaC/Pl/UhAHorFhBgZtL6XdAZRp1tkPwC1NLa9CWs5prLhI85NBQsLdXvjDymG3/EbYfQhVNYqc3TtktQhWLY3ko0QsdMbSEp+64v0NfxyqLbIGdh6M2xHHlLBGqKTyQo4E/nebBgBfe1GpdeywYXc8CT7D3cKXuMXkBy4xN6o5OuKamYp3DVI6uccO9lxgd2CAlJgI2BGgaAgIJV/TYwKqu3WFccjbMuA+bVkWgS2bfnlRbD1Eb1sDyWMmjKYIBgGAWbqKRicfvzBkBIz3V5AKnguWdglQEysQsSuVzOg6ALy1pitA5ykGCsc857BRYcgCSZyFOdvoOigSGoPc5Ta73mgxshIcQE5sHMHd9D7yqITw7JO+GHVMxjhzYLcKPSEgmz3fU+BRy3iYNtiXLaBssCW8KguReqkQOTb3MStV0Ugt4U1eIs1RZWRII6Ww8xeNNItyGGQI4ZMlpg/3lQtkl2JFnBp1imRyFe0kK2Id3PCslMgiQNMS77gvFeDhG3cSkYvheeg/e7ClIh5oh+IAAAAASUVORK5CYII=",ht="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXh7eHl8eXj7+Pn8+eTbH1KAAACPElEQVQozxWPQWrbQABF/0xn3JFKQRTZOIuUsbCCbOgdRoYEOauxkYPcTRyTlPQWIxEltrsRwQ6hK9nEQek6F+gNTE/Q3qLLusv34cN7SH3mFicdYW4gNIhJWXPBRVXzjcFD0IqeU4o4PRbAIVjyico0vJpIifqPfL80QN9DAQY5ucRHE/hpHxBldXe9GilaHKcKMlj6pho2zXgkNdBl0oJ8kiF1DSiJF1ZHBJkQr0Dbux/5I42Zp4cFahJDFGeW6/QjBwmFY/Q7vZ2SnoOdW2parv/Cnm81+m0xrEfiVXQ3W4nOXIqVYi3l6AAQBwMFkViVBANMto4enXHPNTkHBB0oVj4r5vHzCWayrgBvxtygDlDB2CNDjd80ZInY69aKVYZcfJ8DW+fWuc+syEODALx+ojqoafHsthTI+ZW27PGpIeo/cR6YKcbqIuIFhHmBrzAovzIOOJk1ucvcDzrMRYGVBH2yvcAOf0KiKwfRovBI3tm/kW1eemtfNWwIIXE2mJNhvoszfmMBfRCv0OPwd2321uDW3nx2q/BDxFVeoN1g7a6Im8yRnoawa8kbdXnU0cHeTMxKfZGlJgvLb3sKsxgglQnDdAfvj9LUnqWRDo0GiUmPwyU7TAsD7wHeIW3Nfy1qVGKoE9NgJCdYCAexNRob9yCn4DAQmXtQuUtera6bEmTTXhZy6h856xi4mnEl6BI9mfISkLbtJyZIMJIAUd5ZOBEu88KRAk71yxfItj/hpIB0Errv4gO1os4/UICf+o3kkqwAAAAASUVORK5CYII=",Ct="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAD1BMVEX0/PTx+fH2/vbz+/P4//htSO9OAAAC5UlEQVQ4yyWT0QGjMAxDZTsDWKQDmJQBYrgBUsr+M517x0+LRWw9CyA+pC1YzndrMgHaNXVKQ+di13Of1qbur48nWhuRjj8i6ON8e7pNm7zyag/DBTfS9Z4Hup1fUuXMKY4HEE8QOHCByXkIkl7lDT239RtL9quO4JItmmhOAHXg45QuYKrQFLyGJcRvaTw6kQqZy6mkR6JAPFH/XqsQjEDRmUOA+MNLHGyMUT7AHApoAhjgjIJmCxy6XHdf648AWCdGe57IUDazCeTImQOY4/z+eVYVX2IjOw9RydeAeJwl79iGi4HpgQgHEchWraUZLtayu8scq0lHHHUKMY3Ml8hB7CS1jOckDLG9ccgNeX3124phOcjL9fPnWJhTXpLHeG9DRmHnTxHEaHakS2J51lwAJcUraNbuU7q4gMTDQj3Eripc/x+qFM5VEKAB1roQfAkX5/PxqnS2QpOrxfK1Zft0/omV5T+xCSBUAIbEIwUQgvAfxFE1O8dnk233+1UZiqJ1mAbsue6Yt8tF+yOrxC/YrUhzC4qPlE3EbR5hGKhhHdlrg7J9WunV7L7BcYQwAeE59u2tnN1c6gfVYrQiLSZ9OxZdWDXQq0+r0Pbarh3UqGCwauVvbiXuDsNxCtLDdW9rTF8oQYN4EoXXdfmwNguQP26n/tRjDeo+F2W7PjWtfSr6Bn/z+cXOLp4NnMV4RytvSW4B68m+XN9XfZTFGhO/S+cHTuTqZDC21ccA0N7QsePALaDQC3D1f94U9CWo+aq6BjB3v0rxIimBM12296M3aKPHjXLQE9KQKH4By8RHraJ3AgVto2r4xdFqlaPaiAHLl1ZF4P2pI6cYc+K8UZdcmxy7lqGc1IoPxLmIFuIeEZ6j2sQT88muEg1zwrEDTIX5U/ZmcsqfgVlBumiBLF4sAyhf9BFlXOPKLZ4H0iFb3VoHrGhtHTldKrOvP2/reu2zfV8CXMPqzRdlgd0a5eI7WwB/AYcgavcqxXWEAAAAAElFTkSuQmCC",yt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXM2t7O3ODQ3uLR4OTDp25yAAACdUlEQVQozw3P70sTcQDH8c/3/M7NG+j35mnHwjwh4hRy/QFK3zvPNbeIG1koPZmxfj2IDAwihL53zj0JYisfmEHcZJZOiBUG60lZiI8T/ANusuftgQ+kCPIPeMP7hS5mUrV9c1g6MQCAEZ8tDLHwofImAGRlX+SZK3Vu9rRRPuO4PK6/9nA4GIATsxlODS+rdCMhkAZivpYV0LWoQHSLSA4NfUg+6mY+7BKL2++F9LvnrBDYm6JO9i/YO3i/HJTGQ4pdIV82TbEDFG6vGYCd4wZchgK5J2CrKTLE+Tx0v+YGlIbdWJFcQl4ptBN8fUJQN1MCJLcZLYwUVVo+famGGty8EXJF5ofOEDzcodT3/Fb0I5sHmc1ZG7CcSl8COgxlXx09jT05OafjCZLIHJhGIaU6wDZHsuMQ41wbdjmQXbhKnMq1zlXSYrjCnyZblqexA7fC8RxS74tq2P3OxSQwTuJSApH8OZLzBBp1pOe0i3rdyDUA47GySZ31YmC4EQYSXvFSvieORGBxXF9aeVtUWKGS9WMC4Z9Y2uXnJ2nCUXVMbPOYqNYNmGWWQ7Evr+BWC+a0JAMTImcq/S4Z5INdQMeuOqDIMa9beilxfA60iC6sP1INcPDpmHBW8drZHNmqwyddJtVje9q8WGUgWAOzmbU4FCQBFi8B2Wk6pickBnYhJMenmJGuRmtt2IoKq9NuFGbNFR99sHnvrnLsLysKANDIsxbp6RNMAsoDSKuRpMwZbAAzI68QatIjmZ0aImyM3O8/4e2MNlOHZomFsa/fLDsysliHS+nlYLQJMnynxrH8QO4PaAV2Li8B/+52UgeGIVNFYf8B1XG/kFSmLcUAAAAASUVORK5CYII=",It="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXh7vLf7PDj8PTm8/ecW+lZAAACZElEQVQozw2RsU8TUQCHfz3fw7MS87jeI7DdmSMpDEoHE+P0HqGkvRR8vb5XC4NpN2RQZqcK9xJkwtriekcggerC4OZADDiT+A+goxv/gfwB3zd8H/T6vYF/pTZkCSmDNd3CBEtmZJP4N+CvvhecDvmntKsvwB17rpbIRTLOEoYkj9KZzRUuJsuBQFwgptyJ3Y7EL4V+ud5LO1UnMeQSSObqisiISZkbQBlliP3qWSk3GPQXjxv6VF2BTDO4ySx1zhuJXbA2wBNJF4t5vH9keg6wu5NvUpLtXrZ3OHC9ZsgVcZdOl38PM1y/L6m8GRiErj4AqezUjHGatGGIgs5NJDHh8Ua1IuB4035haVT6SaYWMoQ0eJ3rB/Gpnr3fB49YAy1Wa21YKqAHOmAveVw6CCMGMZh5bGtVI7jnZaiQNbta1Z+285oSoKoRbta1KZ/1bBdKH/RIxv2pRVpkoCmvpr097RWoo0CpMlTWllIenSjECU8mV43mHx2fIRfH/pncrJm3+58BWdbSqCS07/yiQnvHiCG4ZPGRFeAtfreoOubyctzHvLNHhjNvIhukxQzjU5O6QdOEzUp1Ef4d98Pxz+IPYX0bcpnT52dbedfz8y7C4R89RV+MjJkuCCx7mWDt4eyK/62lQB55xXGJK7p8u6bgRv4hVHylelYGGFs64W94tng8sAIVqSRJBpqRA9rFvAysS+9ak8s7557pz5HR4qhCRmWgplpTRJ+bhYfSAMO8/YBucWPuSdmFFtOnuWqvV2NbF6CJnbhNDzEZ/T0XSDrUydzkZCG1z/oIEyUFYxW/KPXNfwopuHDcO04UAAAAAElFTkSuQmCC",bt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXm9PXq+Pno9vfs+vttWKBGAAACPElEQVQozw3RQWrbQACF4TfCMjPqZgIj4RRaxsZKE0PuMBZ2cLKaCI9RDAXFmJJknUWWI1O1UlamOMHJSjGkuFn3AD2Cr9CepDrAg+/xIxK4QwIqHHQkUhQ/WuphInVIFBojl8QXc012Tgq4RTtVHWVLZVFh1tEoI91uiN4joCqde8Ukn/zGM1B2W4ari2PtTwyw55Ld+Wways54qhGPyS6FzbIT3lIY8WwWdCq56Yolx6KmSKzoqrsCB5heAp4TGNQWJ1Pc6XlE5jQD5OlIX9I47A9uiUQcPQxcury/ToyxWJG/za6ki88crxKPocKS59Sl3EtBG7C89fCGflpfqoSzCeC4crioJA7F0V5+8MaSIk4qSCdwzpogmbqzEirVpGiS2dOVJvUuuqFEmhHao06KEpq+8lvHI14NJk3Qrmi9vBuRLwAz0qZB4hsDXQFXgtnlpDX3C6ug9BquSw/CYtwAzuTz5vuQNdr/YibhR68378ehZH30FSpjh71LpQkrsj+Q062h5WwZ5wlRoD6uQJy1DqvSYuCUapMBqT5YA4ZFw4KlWapxoUGlKWrx0eDQvmigu4WMYt97ruru98fYL8/0lG6CTOFcFWBhFK5gKw19h2JN808nh7xhkU6sWKLXdtkqBL6h+lULK5k19wFB/FldnGYf3LDeuf6IC2/MzJOSOP0qPxLqzaGIqtBcFIItrstkazONOkrc1D1czjuwEGESB4JJnjgSMN7PXAu7fZQpl1C236C+9mM4Af8P98Ch4R2TRl8AAAAASUVORK5CYII=",wt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXPz8/R0dHT09PU1NToNyAhAAACdElEQVQozw3NP0xTQQDH8d9d7sFrG+QeKVgQ4aoFCwFkYERyLY//0UB8GNGg1WAC0RBGJrzW4mCXQmpgvCYOwEAYiulSpYtza2KiW7s5FgNJFSV2/CzfL7RwpoJ20iadmgA8owOyaxmusKE44scBeb4vIv00dqYgmf6jzWcr7W6INbDQeZbQL9ytXeYgtFfzmW1Fek5msxJlwhyt6qDDxOLQzpVPompYrMPnEnhvLm7M5BxY5nowAj3zkydAkpC0FIG6g7AK+Ub25ybyNWVYwtpseP2rfrQwiGRpfqrnMuPeuvr2dA0p2YsHF2XghkrXKtZ8tLBjR7S2qIaYbKmyLd/QP+EogLjqqwNw5Lq1pDlMLkM5+gNoSvdq+Pxmz9/61EFq6GYM6GqaGvlN95zy3gsmEWI8K3k8OP9OmRLEPO6DP3Wv3g42COinJTZ33dcIvs4ESp6opMTjDs6mcYTEbFeUifuxh989yZrIx4lkpuixxz0nHLCekKbE17suKhYkMGhoYhTZtVBvg4bfq/1L1Im0AGMVpBFwumM0zwyuKiCMi5dqR4Flx47AGyF2xTbxqUdTwCH94BT3DozpLV5WuAL/N8rGtHKjotBOOuOtCJ9E21uqsyBoLOzaXbHPrK5PQBP+fBfeidvJAeMIAmzVt5IkJJ9DBWaZDAepYUhlQqHt0h72SJ3j8TZHom64f516xx9T5evgMPgwG82jZdJaJIDyWp6LAjOCclVyzNA3iTKzIULlBQEPaTXlPHok5gISclmyaWZlqY2aTHdRHpJOwTdDEQ3ZfKtbpclcNhyVClagmY+fIfyKukntPqBgnx5QvZHk/D/MK8JMClrSigAAAABJRU5ErkJggg==",St="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEXe3t7a2trc3Nzg4OCXP9lCAAACoklEQVQozwXBzU/TYBwA4N+QEr4CNbSFwcFuowSqMRvEAwShHWAYNsu7dS0dLnGUSWT4kZB4lGzE4VtcwgIDJqcOWLJxcv4BOoQZuCPxSNSD4WSWLJGL8XmAIiyo2RgJ4A1pxQQlOxRAszLTdnPu2oQGb05RC5slJld7ZAIfo4O44Bn1ud59F0BcjnYOa17Jhwc6EdiKettncsXjT1f8KUBZUW41pK0Jc1Az4dEV3rkkPBtDSZ83Blyt0kSf2PRjzIykoBwINisPbPPtljdVE9iAXRfUPkXLVIgYrCccp5g687NdZbcJ+xa5VE/HhTtT23IKsN5jj/pcUd0dTZNAqCVw72n4gOwnTOC0vvHfaauT8d9zAoRRfPpISZRVyUiw8ELzOG1b2DZpFzkSrHLhq52twDEdyZHwvp2j4uv/bjvOf23/AcEtTuJbY5Cp4YcAer1IGkUzOo2rn8LQOKjFJw3NTw24nprQXY5aF4wxcqcSdbFQ00H4xFl8Drx4X4CikvAM1tuR8bKIBCBoLnKN10KJG4zKAsc7c9WEB9gnCi6BhVjqoco6t20ILAJuVctvaEZK732cRHDRmGfuihOam0o2CHByUZ/epCcVlRs2wmCnMqsd6aSim3ibBJtm1LGyXW3Bb7tJCPlFtUG+SvPdeEUAB60lNdo+VQbLcwRNVtT68FsLcr1+NotgNihlpExS1V2SFgNbeC8bEhgm8sM17wSi6Us2gxVWJU/5GKBpandvfyYbU1yHCLpCgWGbbPXn40rehEsUXKIJr9DMKgICfjc4bl1YfvUhE/YIECGRqjCxSM9hrybAIkND5OeWfFZsXkxB+qDzb7pUQ3EfQ3Ml6EChEt3D+iS01VqC7EQ/Z/DuPQcz4yChoFQJce2Qr+NNAv0HxofmpXGqgHkAAAAASUVORK5CYII=",Et="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAD1BMVEXm5ubo6Ojp6enr6+vt7e1FnZagAAACrklEQVQ4yx1SixUbMQgT3AKAFwDcAfzpBN1/qMrJS5w7bCQhC6IGSUGYQJd6Ox9ZPXi1AGJBavhUTT0JjYPGAab9WcDYIxsmlnxkayX8mhxCmKHA75az5cfRbWybEExiu08xDSgGym0mwuf3j4SvHeQxDJJzh2zp4iOlrD8iOb4SXyC1wiOLRTcnrje+nGamFeXVKWkmzbFIPChkmJ6Fg7mBpV8n+JGOVCd4jv1thThkjeQGNeafpeV3rsEWLfyWc8tC9jOv6FQ8rRzHOOVB+jCYEUAJpDvh8xHNFm/Tm5p5lw94Pp3NhtKEfQsGvnXhowdZE73hPwxKvjDd4i4PCdd0fe3W5fO8ktAsUAacLgstpUw60JCiPLg2XpkgiqPIYYXJd9ksGIT3q+LlevypzItvO+s0F1dBzVr2QDMUkYmuyGcrIS44mVJ7JVKwQXjYuBYp0Uetecbswzsikzu3gUR8bJC/C8Gd/NAzI/xdUGOYQQHDZ8X2d5XuzGRUiXAi9si5CRgoiToRZPtzLJkd0FUHRHZwJf0BHT1sE7gcnh0jmKKlSSF4/GBirGk5+K9NKlGDCfc9JtPhg78JdabH0YQRKNZnJ8tFnPfXHJb4xum1TTCeEmyEdbyEJLjznMLHuFD2Y9NEkSleIBs7SiCbblhgctVi9ch++kDYnn1C9DA5TvdPsToXM55wI6k+8eKT1blwPTqWb5CFJ+7dTBmab+KHy+xwNtItXhZNSpHD2fxnynrxG3ZBKRe8KBpXk11AnadlccEhr9w1nBBvBylNkv7A8eqpGBCDqhitmWQXBjjdS6idr/QjXWLDeMzMbVDoJuM8zN7WenMZWXgZ2vX3F01J3jHZbwk1LRP+DWEvDJtOUoh/AIaBUz5VpWyhuyx4QtgL/NmgC6kM/JvNe+R/C/5aL7BKIbYAAAAASUVORK5CYII=",Ut="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyBAMAAADsEZWCAAAAElBMVEUQERMODxESFBYWGBkaHB0eICLm6ozJAAACkUlEQVQ4yyWTUdLbMAiEASfvoOkBkBy/O5keIE0v8E/uf5h+68qZWALELgu2MG9PP9qyvCzTVhrrsPGOCjvTfXQZvtp/W3Gy6LCITqs4q/DZ+KYl76zKzHVYpY2wNY27nqN1sbLGcrLH3/ENH4oWlGctsDu8AO+HzTLlsYdh8MzP1m6YDMz0ACfcimvakBj+mwO/+5Uta5teOD379sxK1fUxmUhv8MU3jUT5gs26PMephFznkLcpQZ6/dPL9C/GWHcCxDN6oZhD5xBm5qoYBPA+PFE/H1tXDWcWl8TW7rS+4dUzAVy0BIrvC4/HcqW2TkG1HO8q9dC23INAg7NA4AFRFkDTM2lfELPyFzi1VddcpX2z0KjHBUDmdLNJ6dDps4ytrX+FPsZwE31wSL+6OWfHOAJ3+Y0Rk/MiKfmWNPg7oVP/U3Ck9FoCkC2gBpALOiqbMNTkOe8P4FWkTD2Y9Q3+5VmV0uLUJBl68U5uAK2Kl6QDXvLxbwweOL2sixW78uU8p0ysfc7cWrF1j6B1sPJ4WgclYSnJN1bzozrhEcFHmRzBkbJWqqdG+EYJXRFmT5jnLXPUNF6WBdoFbTxYsmDXVLU/WA7MExNc93sJS5hIXDeLxzMScHzdhKvEkibr6cQXYPrmtmTA7JcInISrTzRDvShTdka0uVGrsJAAR6tSn1sKziZtfKVjAxPrJsYgZO0bye+vKTZ/DgoAoLGNO6jYHimZYTL/3pLJHawquJukjBpfz8WOGVSVIWx9ywUfS5iENutidRM4NzkAmxgUSQ68xgNOU+ZLalr4TS2V+D2xqukZig+Z9DilR7Nouzwp1cp/3E5q6Rdlf08obKvAM4qZ6pMr+w3PmQALSSBfjyZn5DwrNRVbywBQiAAAAAElFTkSuQmCC",Dt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEUWGBkYGhsdHyAfISI1t/v6AAAB5ElEQVQozxXQsYoTURSA4f/EeycZsDgDdySDjihk38Hy3GWi2J2BCaziQhaiaB+tt9AFu1kwvYUPsIXNPoB9BAUfwAfwEUzKv/v4odGrroyp9/rUaC6rZ5skv5F8qPsfYYP+yKUMymmAEEeW55oUR4o8jr05KNzJ07yvB7w0KKfLwcQUSjfmMU0PJfPHFoEVU+ohNrcKMEzMQ23FDnVSI2dqtYWI7KlLu6vE4UnyvKc3SJuL7lBbeEEl42ItpGLjzIT8PRJCmkRjVpVpsbJFVN0687okJNZiHAr5Z7MV0BnGIDc+THM1zlbieBc1Fq+tH5BH+OpnbWkj40hSqC8Lw2TvFuF0SUFJCk2IytXbjeqcRAt6NHpnrUkUU4KRzZs8RCK8N/Akn2W04LwxMU/V7XK0bDyN2RxfDyx7I4h5vjZby72V8UnOWumZL3qtYc+8DTE0siSBMXGhywx2dMYPnQHbxdFZ7deiNGxCCtD/QWnbwDoGhRYPDzUdUA3krjpnkvdAgDN4ddLkEQSov9qjd42HaDjI34gEqS9TUueAk+sc4qg5ws407KQYKs8G1jv4xBlqBVk6cb4dISZIwVi1Jzu4+HLk6lyfUxkXvwy+1Q+4WVdHIhwfybZ6CWVhxMEhShOgsP/HOW0MvZJeFwAAAABJRU5ErkJggg==",xt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyAgMAAABjUWAiAAAADFBMVEUWGBkYGhsdHyAfISI1t/v6AAAB5ElEQVQozxXQsYoTURSA4f/EeycZsDgDdySDjihk38Hy3GWi2J2BCaziQhaiaB+tt9AFu1kwvYUPsIXNPoB9BAUfwAfwEUzKv/v4odGrroyp9/rUaC6rZ5skv5F8qPsfYYP+yKUMymmAEEeW55oUR4o8jr05KNzJ07yvB7w0KKfLwcQUSjfmMU0PJfPHFoEVU+ohNrcKMEzMQ23FDnVSI2dqtYWI7KlLu6vE4UnyvKc3SJuL7lBbeEEl42ItpGLjzIT8PRJCmkRjVpVpsbJFVN0687okJNZiHAr5Z7MV0BnGIDc+THM1zlbieBc1Fq+tH5BH+OpnbWkj40hSqC8Lw2TvFuF0SUFJCk2IytXbjeqcRAt6NHpnrUkUU4KRzZs8RCK8N/Akn2W04LwxMU/V7XK0bDyN2RxfDyx7I4h5vjZby72V8UnOWumZL3qtYc+8DTE0siSBMXGhywx2dMYPnQHbxdFZ7deiNGxCCtD/QWnbwDoGhRYPDzUdUA3krjpnkvdAgDN4ddLkEQSov9qjd42HaDjI34gEqS9TUueAk+sc4qg5ws407KQYKs8G1jv4xBlqBVk6cb4dISZIwVi1Jzu4+HLk6lyfUxkXvwy+1Q+4WVdHIhwfybZ6CWVhxMEhShOgsP/HOW0MvZJeFwAAAABJRU5ErkJggg==",$={themes:[{body:"#ede7da url("+gt+") repeat",content:"#ede7da url("+pt+") repeat",popup:"#ede7da url("+ft+") repeat"},{body:"#ede7da url("+mt+") repeat",content:"#ede7da url("+vt+") repeat",popup:"#ede7da url("+Bt+") repeat"},{body:"#ede7da url("+kt+") repeat",content:"#ede7da url("+ht+") repeat",popup:"#ede7da url("+Ct+") repeat"},{body:"#ede7da url("+yt+") repeat",content:"#ede7da url("+It+") repeat",popup:"#ede7da url("+bt+") repeat"},{body:"#ebcece repeat",content:"#f5e4e4 repeat",popup:"#faeceb repeat"},{body:"#ede7da url("+wt+") repeat",content:"#ede7da url("+St+") repeat",popup:"#ede7da url("+Et+") repeat"},{body:"#ede7da url("+Ut+") repeat",content:"#ede7da url("+Dt+") repeat",popup:"#ede7da url("+xt+") repeat"}],fonts:["Microsoft YaHei, PingFangSC-Regular, HelveticaNeue-Light, Helvetica Neue Light, sans-serif","PingFangSC-Regular, -apple-system, Simsun","Kaiti"]},Qt={class:"setting-list"},Vt={class:"theme-list"},Ft=["onClick"],Pt={key:0,class:"iconfont"},Lt={key:1,class:"moon-icon"},Mt={class:"font-list"},Rt=["onClick"],Kt={class:"font-list"},zt={style:{"text-align":"right",margin:"0"}},Ot={class:"font-size"},Wt={class:"resize"},Ht={class:"lang"},Nt={class:"letter-spacing"},Tt={class:"resize"},Jt={class:"lang"},Gt={class:"line-spacing"},qt={class:"resize"},Zt={class:"lang"},Yt={class:"paragraph-spacing"},jt={class:"resize"},Xt={class:"resize"},_t={class:"lang"},$t={key:0,class:"read-width"},eo={class:"resize"},to={class:"lang"},oo={class:"paragraph-spacing"},no={class:"resize"},so={class:"resize"},io={class:"lang"},ao={class:"infinite-loading"},ro=Ae({__name:"ReadSettings",setup(d){const s=le(),u=Xe(()=>ce.saveReadConfig(s.config),500);Se(()=>s.config,()=>{u()},{deep:2});const B=l(()=>s.theme),n=l(()=>s.isNight),I=l(()=>B.value==6?"":""),p=[{background:"rgba(250, 245, 235, 0.8)"},{background:"rgba(245, 234, 204, 0.8)"},{background:"rgba(230, 242, 230, 0.8)"},{background:"rgba(228, 241, 245, 0.8)"},{background:"rgba(245, 228, 228, 0.8)"},{background:"rgba(224, 224, 224, 0.8)"},{background:"rgba(0, 0, 0, 0.5)"}],w=l(()=>({background:$.themes[B.value].popup})),k=F=>{s.config.theme=F},V=U(["雅黑","宋体","楷书"]),x=F=>{s.config.font=F},E=l(()=>s.config.font),y=U(s.config.customFontName),Q=U(!1),h=()=>{Q.value=!1,s.config.font=-1,s.config.customFontName=y.value},C=()=>{Q.value=!1,Ke.prompt("请输入 字体网络链接","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^https?:.+$/,inputErrorMessage:"url 形式不正确",beforeClose:(F,e,W)=>{if(F==="confirm"){e.confirmButtonLoading=!0,e.confirmButtonText="下载中……";const G=e.inputValue;if(typeof FontFace!="function")return L.error("浏览器不支持FontFace"),W();const Y=new FontFace(y.value,`url("${G}")`);document.fonts.add(Y),Y.load().then(function(){e.confirmButtonLoading=!1,L.info("字体加载成功！"),h(),W()}).catch(function(S){throw e.confirmButtonLoading=!1,e.confirmButtonText="确定",L.error("下载失败，请检查您输入的 url"),S})}else W()}})},a=l(()=>s.config.fontSize),c=()=>{s.config.fontSize<48&&(s.config.fontSize+=2)},A=()=>{s.config.fontSize>12&&(s.config.fontSize-=2)},g=l(()=>s.config.spacing),b=()=>{s.config.spacing.letter-=.01},te=()=>{s.config.spacing.letter+=.01},ve=()=>{s.config.spacing.line-=.1},Be=()=>{s.config.spacing.line+=.1},ke=()=>{s.config.spacing.paragraph-=.1},ue=()=>{s.config.spacing.paragraph+=.1},he=l(()=>s.config.readWidth),ge=()=>{s.config.readWidth+160+2*68>window.innerWidth||(s.config.readWidth+=160)},Ce=()=>{s.config.readWidth>640&&(s.config.readWidth-=160)},ye=l(()=>s.config.jumpDuration),q=()=>{s.config.jumpDuration+=100},Ie=()=>{s.config.jumpDuration!==0&&(s.config.jumpDuration-=100)},pe=l(()=>s.config.infiniteLoading),Z=F=>{s.config.infiniteLoading=F};return(F,e)=>{const W=qe,G=je,Y=Re;return f(),m("div",{class:D(["settings-wrapper",{night:i(n),day:!i(n)}]),style:J(i(w))},[e[51]||(e[51]=t("div",{class:"settings-title"},"设置",-1)),t("div",Qt,[t("ul",null,[t("li",Vt,[e[7]||(e[7]=t("i",null,"阅读主题",-1)),(f(),m(ee,null,re(p,(S,P)=>t("span",{class:D(["theme-item",{selected:i(B)==P}]),key:P,style:J(S),ref_for:!0,ref:"themes",onClick:H=>k(P)},[P<6?(f(),m("em",Pt,"")):(f(),m("em",Lt,M(i(I)),1))],14,Ft)),64))]),t("li",Mt,[e[8]||(e[8]=t("i",null,"正文字体",-1)),(f(!0),m(ee,null,re(i(V),(S,P)=>(f(),m("span",{class:D(["font-item",{selected:i(E)==P}]),key:P,onClick:H=>x(P)},M(S),11,Rt))),128))]),t("li",Kt,[e[14]||(e[14]=t("i",null,"自定字体",-1)),K(W,{effect:"dark",content:"自定义的字体名称",placement:"top"},{default:R(()=>[Ze(t("input",{type:"text",class:"font-item font-item-input","onUpdate:modelValue":e[0]||(e[0]=S=>me(y)?y.value=S:null),placeholder:"请输入自定义的字体名称"},null,512),[[Ye,i(y)]])]),_:1}),K(Y,{placement:"top",width:"270",trigger:"click",visible:i(Q),"onUpdate:visible":e[4]||(e[4]=S=>me(Q)?Q.value=S:null)},{reference:R(()=>e[12]||(e[12]=[t("span",{type:"text",class:"font-item"},"保存",-1)])),default:R(()=>[e[13]||(e[13]=t("p",null," 已经安装在您的设备上的字体请确认输入的字体名称完整无误，或者从网络下载字体。 ",-1)),t("div",zt,[K(G,{size:"small",plain:"",onClick:e[1]||(e[1]=S=>Q.value=!1)},{default:R(()=>e[9]||(e[9]=[T("取消")])),_:1,__:[9]}),K(G,{type:"primary",size:"small",onClick:e[2]||(e[2]=S=>h())},{default:R(()=>e[10]||(e[10]=[T("确定")])),_:1,__:[10]}),K(G,{type:"primary",size:"small",onClick:e[3]||(e[3]=S=>C())},{default:R(()=>e[11]||(e[11]=[T("网络下载")])),_:1,__:[11]})])]),_:1,__:[13]},8,["visible"])]),t("li",Ot,[e[20]||(e[20]=t("i",null,"字体大小",-1)),t("div",Wt,[t("span",{class:"less",onClick:A},e[15]||(e[15]=[t("em",{class:"iconfont"},"",-1)])),e[17]||(e[17]=t("b",null,null,-1)),e[18]||(e[18]=T()),t("span",Ht,M(i(a)),1),e[19]||(e[19]=t("b",null,null,-1)),t("span",{class:"more",onClick:c},e[16]||(e[16]=[t("em",{class:"iconfont"},"",-1)]))])]),t("li",Nt,[e[26]||(e[26]=t("i",null,"字距",-1)),t("div",Tt,[t("span",{class:"less",onClick:b},e[21]||(e[21]=[t("em",{class:"iconfont"},"",-1)])),e[23]||(e[23]=t("b",null,null,-1)),e[24]||(e[24]=T()),t("span",Jt,M(i(g).letter.toFixed(2)),1),e[25]||(e[25]=t("b",null,null,-1)),t("span",{class:"more",onClick:te},e[22]||(e[22]=[t("em",{class:"iconfont"},"",-1)]))])]),t("li",Gt,[e[32]||(e[32]=t("i",null,"行距",-1)),t("div",qt,[t("span",{class:"less",onClick:ve},e[27]||(e[27]=[t("em",{class:"iconfont"},"",-1)])),e[29]||(e[29]=t("b",null,null,-1)),e[30]||(e[30]=T()),t("span",Zt,M(i(g).line.toFixed(1)),1),e[31]||(e[31]=t("b",null,null,-1)),t("span",{class:"more",onClick:Be},e[28]||(e[28]=[t("em",{class:"iconfont"},"",-1)]))])]),t("li",Yt,[e[37]||(e[37]=t("i",null,"段距",-1)),t("div",jt,[t("div",Xt,[t("span",{class:"less",onClick:ke},e[33]||(e[33]=[t("em",{class:"iconfont"},"",-1)])),e[35]||(e[35]=t("b",null,null,-1)),t("span",_t,M(i(g).paragraph.toFixed(1)),1),e[36]||(e[36]=t("b",null,null,-1)),t("span",{class:"more",onClick:ue},e[34]||(e[34]=[t("em",{class:"iconfont"},"",-1)]))])])]),i(s).miniInterface?fe("",!0):(f(),m("li",$t,[e[43]||(e[43]=t("i",null,"页面宽度",-1)),t("div",eo,[t("span",{class:"less",onClick:Ce},e[38]||(e[38]=[t("em",{class:"iconfont"},"",-1)])),e[40]||(e[40]=t("b",null,null,-1)),e[41]||(e[41]=T()),t("span",to,M(i(he)),1),e[42]||(e[42]=t("b",null,null,-1)),t("span",{class:"more",onClick:ge},e[39]||(e[39]=[t("em",{class:"iconfont"},"",-1)]))])])),t("li",oo,[e[49]||(e[49]=t("i",null,"翻页速度",-1)),t("div",no,[t("div",so,[t("span",{class:"less",onClick:Ie},e[44]||(e[44]=[t("em",{class:"iconfont"},"",-1)])),e[46]||(e[46]=t("b",null,null,-1)),e[47]||(e[47]=T()),t("span",io,M(i(ye)),1),e[48]||(e[48]=t("b",null,null,-1)),t("span",{class:"more",onClick:q},e[45]||(e[45]=[t("em",{class:"iconfont"},"",-1)]))])])]),t("li",ao,[e[50]||(e[50]=t("i",null,"无限加载",-1)),(f(),m("span",{class:D(["infinite-loading-item",{selected:i(pe)==!1}]),key:0,onClick:e[5]||(e[5]=S=>Z(!1))},"关闭",2)),(f(),m("span",{class:D(["infinite-loading-item",{selected:i(pe)==!0}]),key:1,onClick:e[6]||(e[6]=S=>Z(!0))},"开启",2))])])])],6)}}}),lo=de(ro,[["__scopeId","data-v-dd7cfcb2"]]),co={class:"wrapper"},Ao=["onClick"],uo=Ae({__name:"CatalogItem",props:{index:{},source:{},gotoChapter:{type:Function},currentChapterIndex:{}},setup(d){const s=d,u=n=>n==s.currentChapterIndex,B=l(()=>{const n=s.source;return"catas"in n?n.catas:[s.source]});return(n,I)=>(f(),m("div",co,[(f(!0),m(ee,null,re(i(B),p=>(f(),m("div",{class:D(["cata-text",{selected:u(p.index)}]),key:p.url,onClick:w=>n.gotoChapter(p)},M(p.title),11,Ao))),128))]))}}),go=de(uo,[["__scopeId","data-v-a892cd6d"]]),po=Ae({__name:"PopCatalog",emits:["getContent"],setup(d,{emit:s}){const u=le(),{catalog:B,popCataVisible:n,miniInterface:I}=ze(u),p=l(()=>u.theme),w=l(()=>u.theme),k=l(()=>({background:$.themes[w.value].popup})),V=l(()=>{const C=B.value;if(I.value)return C;const a=Math.ceil(C.length/2),c=new Array(a);let A=0;for(;A<a;)c[A]={index:A,catas:C.slice(2*A,2*A+2)},A++;return c}),x=U(),E=l({get:()=>u.readingBook.chapterIndex,set:C=>u.readingBook.chapterIndex=C}),y=l(()=>{const C=E.value;return I.value?C:Math.floor(C/2)});_e(()=>{n.value&&x.value.scrollToIndex(y.value)});const Q=s,h=C=>{const a=B.value.indexOf(C);E.value=a,u.setPopCataVisible(!1),u.setContentLoading(!0),u.saveBookProgress(),Q("getContent",a)};return(C,a)=>(f(),m("div",{class:D({"cata-wrapper":!0,visible:i(n)}),style:J(i(k))},[a[0]||(a[0]=t("div",{class:"title"},"目录",-1)),K(i($e),{style:{height:"300px",overflow:"auto"},class:D({night:i(p),day:!i(p)}),ref_key:"virtualListRef",ref:x,"data-key":"index","wrap-class":"data-wrapper","item-class":"cata","data-sources":i(V),"data-component":go,"estimate-size":40,"extra-props":{gotoChapter:h,currentChapterIndex:i(E)}},null,8,["class","data-sources","extra-props"])],6))}}),fo=de(po,[["__scopeId","data-v-6cab38af"]]),mo={class:"tools"},vo={class:"tools"},Bo={key:0},ko={key:0},ho={class:"content"},Co=["chapterIndex"],yo=Ae({__name:"BookChapter",setup(d){const s=U(),{isLoading:u,loadingWrapper:B}=it(s,"正在获取信息"),n=le(),{catalog:I,popCataVisible:p,readSettingsVisible:w,miniInterface:k,showContent:V,bookProgress:x,theme:E,isNight:y}=ze(n),Q=l({get:()=>n.readingBook.chapterPos,set:o=>n.readingBook.chapterPos=o}),h=l({get:()=>n.readingBook.chapterIndex,set:o=>n.readingBook.chapterIndex=o}),C=l({get:()=>n.readingBook.isSeachBook,set:o=>n.readingBook.isSeachBook=o});Se(()=>n.readingBook,o=>{localStorage.setItem("readingRecent",JSON.stringify(o)),sessionStorage.setItem("chapterIndex",o.chapterIndex.toString()),sessionStorage.setItem("chapterPos",o.chapterPos.toString())},{deep:1});const a=l(()=>n.config.infiniteLoading);let c;const A=U();Ve(()=>{a.value?c==null||c.observe(A.value):c==null||c.disconnect()});const g=()=>{const o=H.value.slice(-1)[0].index;I.value.length-1>o&&(oe(o+1,!1),n.saveBookProgress())},b=o=>{if(!u.value)for(const{isIntersecting:r}of o){if(!r)return;g()}},te=l(()=>n.config.font>=0?$.fonts[n.config.font]:n.config.customFontName),ve=l(()=>n.config.fontSize+"px"),Be=l(()=>$.themes[E.value].body),ke=l(()=>$.themes[E.value].content),ue=l(()=>$.themes[E.value].popup),he=l(()=>k.value?window.innerWidth+"px":n.config.readWidth-130+"px"),ge=l(()=>k.value?window.innerWidth-33:n.config.readWidth-33),Ce=l(()=>({background:Be.value})),ye=l(()=>({background:ke.value,width:he.value})),q=U(!1),Ie=l(()=>({background:ue.value,marginLeft:k.value?0:-(n.config.readWidth/2+68)+"px",display:k.value&&!q.value?"none":"block"})),pe=l(()=>({background:ue.value,marginRight:k.value?0:-(n.config.readWidth/2+52)+"px",display:k.value&&!q.value?"none":"block"})),Z=()=>{n.setMiniInterface(window.innerWidth<776);const o=n.config.readWidth;F(o)},F=o=>{n.miniInterface||(o<640&&(n.config.readWidth=640),o+2*68>window.innerWidth&&(n.config.readWidth-=160))};Se(()=>n.config.readWidth,o=>F(o));const e=U(),W=U(),G=()=>{_(e.value)},Y=()=>{_(W.value)},S=et(),P=()=>{S.push("/")},H=U([]),j=U(!0),oe=(o,r=!0,z=0)=>{r&&(n.setShowContent(!1),_(e.value,{duration:0}),Ee(o,z),H.value=[]);const O=n.readingBook.bookUrl,{title:N,index:se}=I.value[o];B(ce.getBookContent(O,se).then(v=>{if(v.data.isSuccess){const ie=v.data.data.split(/\n+/),Je=encodeURIComponent(O);for(let ae=0;ae<ie.length;ae++)/^\s*<img[^>]*src[^>]+>$/.test(ie[ae])||(ie[ae]=ie[ae].replace(new RegExp('img src="',"g"),`img src="/image?url=${Je}&path=`));H.value.push({index:o,content:ie,title:N}),r&&We(z)}else{L({message:v.data.errorMsg,type:"error"});const X=[v.data.errorMsg];H.value.push({index:o,content:X,title:N})}if(n.setContentLoading(!0),j.value=!1,n.setShowContent(!0),!v.data.isSuccess)throw v.data},v=>{const X=["获取章节内容失败！"];throw H.value.push({index:o,content:X,title:N}),n.setShowContent(!0),v}))},Oe=U(),be=U(),We=o=>{Me(()=>{be.value.length===1&&be.value[0].scrollToReadedLength(o)})},He=tt(()=>n.saveBookProgress(),6e4),Ne=(o,r)=>{Ee(o,r),He()};Ve(()=>{var o;document.title=((o=I.value[h.value])==null?void 0:o.title)||document.title});const Ee=(o,r)=>{h.value=o,Q.value=r},Ue=()=>{const o=x.value;document.visibilityState=="hidden"&&o&&n.saveBookProgress()},De=()=>{n.setContentLoading(!0);const o=h.value+1;typeof I.value[o]<"u"?(L({message:"下一章",type:"info"}),oe(o),n.saveBookProgress()):L({message:"本章是最后一章",type:"error"})},xe=()=>{n.setContentLoading(!0);const o=h.value-1;typeof I.value[o]<"u"?(L({message:"上一章",type:"info"}),oe(o),n.saveBookProgress()):L({message:"本章是第一章",type:"error"})};let ne=!0;const we=o=>{if(ne)switch(o.key){case"ArrowLeft":o.stopPropagation(),o.preventDefault(),xe();break;case"ArrowRight":o.stopPropagation(),o.preventDefault(),De();break;case"ArrowUp":o.stopPropagation(),o.preventDefault(),document.documentElement.scrollTop===0?L.warning("已到达页面顶部"):(ne=!1,_(0-document.documentElement.clientHeight+100,{duration:n.config.jumpDuration,callback:()=>ne=!0}));break;case"ArrowDown":o.stopPropagation(),o.preventDefault(),document.documentElement.clientHeight+document.documentElement.scrollTop===document.documentElement.scrollHeight?L.warning("已到达页面底部"):(ne=!1,_(document.documentElement.clientHeight-100,{duration:n.config.jumpDuration,callback:()=>ne=!0}));break}},Qe=o=>{(o.key==="ArrowUp"||o.key==="ArrowDown")&&(o.preventDefault(),o.stopPropagation())};Pe(async()=>{await n.loadWebConfig();const o=sessionStorage.getItem("bookUrl"),r=sessionStorage.getItem("bookName"),z=sessionStorage.getItem("bookAuthor"),O=Number(sessionStorage.getItem("chapterIndex")||0),N=Number(sessionStorage.getItem("chapterPos")||0),se=sessionStorage.getItem("isSeachBook")==="true";if(Fe(o)||Fe(r)||z===null)return L.warning("书籍信息为空，即将自动返回书架页面..."),setTimeout(P,500);const v={bookUrl:o,name:r,author:z,chapterIndex:O,chapterPos:N,isSeachBook:se};Z(),window.addEventListener("resize",Z),B(n.loadWebCatalog(v).then(X=>{n.setReadingBook(v),oe(O,!0,N),window.addEventListener("keyup",we),window.addEventListener("keydown",Qe),document.addEventListener("visibilitychange",Ue),c=new IntersectionObserver(b,{rootMargin:"-100% 0% 20% 0%"}),a.value===!0&&c.observe(A.value),document.title="...",document.title=r+" | "+X[O].title}))}),Le(()=>{window.removeEventListener("keyup",we),window.removeEventListener("keydown",Qe),window.removeEventListener("resize",Z),document.removeEventListener("visibilitychange",Ue),w.value=!1,p.value=!1,c==null||c.disconnect(),c=null});const Te=async()=>{const o=n.readingBook;o.isSeachBook===!0&&await Ke.confirm(`是否将《${o.name}》放入书架？`,"放入书架",{confirmButtonText:"确认",cancelButtonText:"否",type:"info",closeOnHashChange:!1}).then(()=>{C.value=!1}).catch(async()=>{await ce.deleteBook(o)}).finally(()=>sessionStorage.removeItem("isSeachBook"))};return ot(async(o,r,z)=>{window.removeEventListener("keyup",we),await Te(),z()}),(o,r)=>{const z=fo,O=Re,N=lo,se=ut;return f(),m("div",{class:D(["chapter-wrapper",{night:i(y),day:!i(y)}]),style:J(i(Ce)),onClick:r[2]||(r[2]=v=>q.value=!i(q))},[t("div",{class:"tool-bar",style:J(i(Ie))},[t("div",mo,[K(O,{placement:"right",width:i(ge),trigger:"click","show-arrow":!1,visible:i(p),"onUpdate:visible":r[0]||(r[0]=v=>me(p)?p.value=v:null),"popper-class":"pop-cata"},{reference:R(()=>[t("div",{class:D(["tool-icon",{"no-point":!1}])},r[3]||(r[3]=[t("div",{class:"iconfont"},"",-1),t("div",{class:"icon-text"},"目录",-1)]))]),default:R(()=>[K(z,{onGetContent:oe,class:"popup"})]),_:1},8,["width","visible"]),K(O,{placement:"right",width:i(ge),trigger:"click","show-arrow":!1,visible:i(w),"onUpdate:visible":r[1]||(r[1]=v=>me(w)?w.value=v:null),"popper-class":"pop-setting"},{reference:R(()=>[t("div",{class:D(["tool-icon",{"no-point":i(j)}])},r[4]||(r[4]=[t("div",{class:"iconfont"},"",-1),t("div",{class:"icon-text"},"设置",-1)]),2)]),default:R(()=>[K(N,{class:"popup"})]),_:1},8,["width","visible"]),t("div",{class:"tool-icon",onClick:P},r[5]||(r[5]=[t("div",{class:"iconfont"},"",-1),t("div",{class:"icon-text"},"书架",-1)])),t("div",{class:D(["tool-icon",{"no-point":i(j)}]),onClick:G},r[6]||(r[6]=[t("div",{class:"iconfont"},"",-1),t("div",{class:"icon-text"},"顶部",-1)]),2),t("div",{class:D(["tool-icon",{"no-point":i(j)}]),onClick:Y},r[7]||(r[7]=[t("div",{class:"iconfont"},"",-1),t("div",{class:"icon-text"},"底部",-1)]),2)])],4),t("div",{class:"read-bar",style:J(i(pe))},[t("div",vo,[t("div",{class:D(["tool-icon",{"no-point":i(j)}]),onClick:xe},[r[8]||(r[8]=t("div",{class:"iconfont"},"",-1)),i(k)?(f(),m("span",Bo,"上一章")):fe("",!0)],2),t("div",{class:D(["tool-icon",{"no-point":i(j)}]),onClick:De},[i(k)?(f(),m("span",ko,"下一章")):fe("",!0),r[9]||(r[9]=t("div",{class:"iconfont"},"",-1))],2)])],4),r[10]||(r[10]=t("div",{class:"chapter-bar"},null,-1)),t("div",{class:"chapter",ref_key:"content",ref:s,style:J(i(ye))},[t("div",ho,[t("div",{class:"top-bar",ref_key:"top",ref:e},null,512),(f(!0),m(ee,null,re(i(H),v=>(f(),m("div",{key:v.index,chapterIndex:v.index,ref_for:!0,ref_key:"chapter",ref:Oe},[i(V)?(f(),nt(se,{key:0,ref_for:!0,ref_key:"chapterRef",ref:be,chapterIndex:v.index,contents:v.content,title:v.title,spacing:i(n).config.spacing,fontSize:i(ve),fontFamily:i(te),onReadedLengthChange:Ne},null,8,["chapterIndex","contents","title","spacing","fontSize","fontFamily"])):fe("",!0)],8,Co))),128)),t("div",{class:"loading",ref_key:"loading",ref:A},null,512),t("div",{class:"bottom-bar",ref_key:"bottom",ref:W},null,512)])],4)],6)}}}),So=de(yo,[["__scopeId","data-v-59949dc9"]]);export{So as default};
