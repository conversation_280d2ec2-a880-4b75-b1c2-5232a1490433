[{"customOrder": 2, "enableJs": true, "enabled": true, "singleUrl": true, "sourceGroup": "legado", "sourceIcon": "https://cdn.jsdelivr.net/gh/gedoor/legado@master/app/src/main/res/mipmap-hdpi/ic_launcher.png", "sourceName": "使用说明", "sourceUrl": "https://www.yuque.com/legado"}, {"customOrder": 3, "enableJs": true, "enabled": true, "singleUrl": true, "sourceGroup": "legado", "sourceIcon": "http://mmbiz.qpic.cn/mmbiz_png/hpfMV8hEuL2eS6vnCxvTzoOiaCAibV6exBzJWq9xMic9xDg3YXAick87tsfafic0icRwkQ5ibV0bJ84JtSuxhPuEDVquA/0?wx_fmt=png", "sourceName": "小说拾遗", "sourceUrl": "snssdk1128://user/profile/562564899806367"}, {"customOrder": 4, "enableJs": true, "enabled": true, "singleUrl": true, "sourceGroup": "legado", "sourceIcon": "https://cdn.jsdelivr.net/gh/mgz0227/meowcloud/icon.png", "sourceName": "Me<PERSON>云", "sourceUrl": "https://pan.miaogongzi.net"}, {"customOrder": 5, "enableJs": true, "enabled": true, "singleUrl": true, "sourceGroup": "legado", "sourceIcon": "https://cdn.jsdelivr.net/gh/gedoor/legado@master/app/src/main/res/mipmap-hdpi/ic_launcher.png", "sourceName": "烏雲净化", "sourceUrl": "https://www.lanzoux.com/b0bw8jwoh"}]