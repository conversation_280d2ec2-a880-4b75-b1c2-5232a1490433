package io.legado.app.constant

@Suppress("ConstPropertyName")
object PreferKey {
    const val language = "language"
    const val fontScale = "fontScale"
    const val themeMode = "themeMode"
    const val userAgent = "userAgent"
    const val showUnread = "showUnread"
    const val bookGroupStyle = "bookGroupStyle"
    const val useDefaultCover = "useDefaultCover"
    const val loadCoverOnlyWifi = "loadCoverOnlyWifi"
    const val coverShowName = "coverShowName"
    const val coverShowAuthor = "coverShowAuthor"
    const val coverShowNameN = "coverShowNameN"
    const val coverShowAuthorN = "coverShowAuthorN"
    const val remoteServerId = "remoteServerId"
    const val hideStatusBar = "hideStatusBar"
    const val clickActionTL = "clickActionTopLeft"
    const val clickActionTC = "clickActionTopCenter"
    const val clickActionTR = "clickActionTopRight"
    const val clickActionML = "clickActionMiddleLeft"
    const val clickActionMC = "clickActionMiddleCenter"
    const val clickActionMR = "clickActionMiddleRight"
    const val clickActionBL = "clickActionBottomLeft"
    const val clickActionBC = "clickActionBottomCenter"
    const val clickActionBR = "clickActionBottomRight"
    const val hideNavigationBar = "hideNavigationBar"
    const val precisionSearch = "precisionSearch"
    const val readAloudByPage = "readAloudByPage"
    const val ttsEngine = "appTtsEngine"
    const val ttsFollowSys = "ttsFollowSys"
    const val ttsSpeechRate = "ttsSpeechRate"
    const val prevKeys = "prevKeyCodes"
    const val nextKeys = "nextKeyCodes"
    const val showDiscovery = "showDiscovery"
    const val enableReview = "enableReview"
    const val showRss = "showRss"
    const val bookshelfLayout = "bookshelfLayout"
    const val bookshelfSort = "bookshelfSort"
    const val bookExportFileName = "bookExportFileName"
    const val bookImportFileName = "bookImportFileName"
    const val episodeExportFileName = "episodeExportFileName"
    const val recordLog = "recordLog"
    const val processText = "process_text"
    const val cleanCache = "cleanCache"
    const val saveTabPosition = "saveTabPosition"
    const val fontFolder = "fontFolder"
    const val backupPath = "backupUri"
    const val restoreIgnore = "restoreIgnore"
    const val threadCount = "threadCount"
    const val webPort = "webPort"
    const val keepLight = "keep_light"
    const val webService = "webService"
    const val webDavUrl = "web_dav_url"
    const val webDavAccount = "web_dav_account"
    const val webDavPassword = "web_dav_password"
    const val webDavDir = "webDavDir"
    const val enableCustomExport = "enableCustomExport"
    const val exportToWebDav = "webDavCacheBackup"
    const val exportNoChapterName = "exportNoChapterName"
    const val exportType = "exportType"
    const val exportPictureFile = "exportPictureFile"
    const val changeSourceCheckAuthor = "changeSourceCheckAuthor"
    const val changeSourceLoadToc = "changeSourceLoadToc"
    const val changeSourceLoadInfo = "changeSourceLoadInfo"
    const val changeSourceLoadWordCount = "changeSourceLoadWordCount"
    const val chineseConverterType = "chineseConverterType"
    const val launcherIcon = "launcherIcon"
    const val textSelectAble = "selectText"
    const val shareLayout = "shareLayout"
    const val comicStyleSelect = "comicStyleSelect"
    const val readStyleSelect = "readStyleSelect"
    const val systemTypefaces = "system_typefaces"
    const val readBodyToLh = "readBodyToLh"
    const val textFullJustify = "textFullJustify"
    const val textBottomJustify = "textBottomJustify"
    const val autoReadSpeed = "autoReadSpeed"
    const val barElevation = "barElevation"
    const val transparentStatusBar = "transparentStatusBar"
    const val immNavigationBar = "immNavigationBar"
    const val defaultCover = "defaultCover"
    const val defaultCoverDark = "defaultCoverDark"
    const val replaceEnableDefault = "replaceEnableDefault"
    const val showBrightnessView = "showBrightnessView"
    const val autoClearExpired = "autoClearExpired"
    const val autoChangeSource = "autoChangeSource"
    const val importKeepName = "importKeepName"
    const val importKeepGroup = "importKeepGroup"
    const val screenOrientation = "screenOrientation"
    const val syncBookProgress = "syncBookProgress"
    const val syncBookProgressPlus = "syncBookProgressPlus"
    const val cronet = "Cronet"
    const val antiAlias = "antiAlias"
    const val bitmapCacheSize = "bitmapCacheSize"
    const val imageRetainNum = "imageRetainNum"
    const val preDownloadNum = "preDownloadNum"
    const val mangaPreDownloadNum = "mangaPreDownloadNum"
    const val mangaAutoPageSpeed = "mangaAutoPageSpeed"
    const val mangaFooterConfig = "mangaFooterConfig"
    const val disableClickScroll = "disableClickScroll"
    const val enableMangaHorizontalScroll = "enableMangaHorizontalScroll"
    const val hideMangaTitle = "hideMangaTitle"
    const val mangaColorFilter = "mangaColorFilter"
    const val enableMangaEInk = "enableMangaEInk"
    const val mangaEInkThreshold = "mangaEInkThreshold"
    const val disableHorizontalAnimator = "disableHorizontalAnimator"
    const val enableMangaGray = "enableMangaGray"
    const val autoRefresh = "auto_refresh"
    const val defaultToRead = "defaultToRead"
    const val exportCharset = "exportCharset"
    const val exportUseReplace = "exportUseReplace"
    const val useZhLayout = "useZhLayout"
    const val brightness = "brightness"
    const val nightBrightness = "nightBrightness"
    const val expandTextMenu = "expandTextMenu"
    const val doublePageHorizontal = "doubleHorizontalPage"
    const val readUrlOpenInBrowser = "readUrlInBrowser"
    const val defaultBookTreeUri = "defaultBookTreeUri"
    const val checkSource = "checkSource"
    const val uploadRule = "uploadRule"
    const val tocUiUseReplace = "tocUiUseReplace"
    const val tocCountWords = "tocCountWords"
    const val enableReadRecord = "enableReadRecord"
    const val localBookImportSort = "localBookImportSort"
    const val customWelcome = "customWelcome"
    const val welcomeImage = "welcomeImagePath"
    const val welcomeImageDark = "welcomeImagePathDark"
    const val welcomeShowText = "welcomeShowText"
    const val welcomeShowTextDark = "welcomeShowTextDark"
    const val welcomeShowIcon = "welcomeShowIcon"
    const val welcomeShowIconDark = "welcomeShowIconDark"
    const val pageTouchSlop = "pageTouchSlop"
    const val showAddToShelfAlert = "showAddToShelfAlert"
    const val ignoreAudioFocus = "ignoreAudioFocus"
    const val parallelExportBook = "parallelExportBook"
    const val progressBarBehavior = "progressBarBehavior"
    const val sourceEditMaxLine = "sourceEditMaxLine"
    const val ttsTimer = "ttsTimer"
    const val noAnimScrollPage = "noAnimScrollPage"
    const val webDavDeviceName = "webDavDeviceName"
    const val webServiceWakeLock = "webServiceWakeLock"
    const val audioPlayWakeLock = "audioPlayWakeLock"
    const val readAloudWakeLock = "readAloudWakeLock"
    const val showLastUpdateTime = "showLastUpdateTime"
    const val showWaitUpCount = "showWaitUpCount"
    const val clearWebViewData = "clearWebViewData"
    const val onlyLatestBackup = "onlyLatestBackup"
    const val brightnessVwPos = "brightnessVwPos"
    const val shrinkDatabase = "shrinkDatabase"
    const val batchChangeSourceDelay = "batchChangeSourceDelay"
    const val openBookInfoByClickTitle = "openBookInfoByClickTitle"
    const val defaultHomePage = "defaultHomePage"
    const val showBookshelfFastScroller = "showBookshelfFastScroller"
    const val importKeepEnable = "importKeepEnable"
    const val previewImageByClick = "previewImageByClick"
    const val keyPageOnLongPress = "keyPageOnLongPress"
    const val volumeKeyPage = "volumeKeyPage"
    const val volumeKeyPageOnPlay = "volumeKeyPageOnPlay"
    const val mouseWheelPage = "mouseWheelPage"
    const val recordHeapDump = "recordHeapDump"
    const val optimizeRender = "optimizeRender"
    const val updateToVariant = "updateToVariant"
    const val streamReadAloudAudio = "streamReadAloudAudio"
    const val pauseReadAloudWhilePhoneCalls = "pauseReadAloudWhilePhoneCalls"
    const val readAloudByMediaButton = "readAloudByMediaButton"
    const val showMangaUi = "showMangaUi"
    const val disableMangaScale = "disableMangaScale"
    const val paddingDisplayCutouts = "paddingDisplayCutouts"

    const val cPrimary = "colorPrimary"
    const val cAccent = "colorAccent"
    const val cBackground = "colorBackground"
    const val cBBackground = "colorBottomBackground"
    const val bgImage = "backgroundImage"
    const val bgImageBlurring = "backgroundImageBlurring"

    const val cNPrimary = "colorPrimaryNight"
    const val cNAccent = "colorAccentNight"
    const val cNBackground = "colorBackgroundNight"
    const val cNBBackground = "colorBottomBackgroundNight"
    const val bgImageN = "backgroundImageNight"
    const val bgImageNBlurring = "backgroundImageNightBlurring"
    const val showReadTitleAddition = "showReadTitleAddition"
    const val readBarStyleFollowPage = "readBarStyleFollowPage"
    const val contentSelectSpeakMod = "contentReadAloudMod"
}
