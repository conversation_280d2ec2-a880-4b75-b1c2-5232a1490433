package io.legado.app.constant

object EventBus {
    const val MEDIA_BUTTON = "mediaButton"
    const val RECREATE = "RECREATE"
    const val UP_BOOKSHELF = "upBookToc"
    const val BOOKSHELF_REFRESH = "bookshelfRefresh"
    const val ALOUD_STATE = "aloud_state"
    const val TTS_PROGRESS = "ttsStart"
    const val AUDIO_DS = "audioDs"
    const val READ_ALOUD_DS = "readAloudDs"
    const val BATTERY_CHANGED = "batteryChanged"
    const val TIME_CHANGED = "timeChanged"
    const val UP_CONFIG = "upConfig"
    const val AUDIO_SUB_TITLE = "audioSubTitle"
    const val AUDIO_STATE = "audioState"
    const val AUDIO_PROGRESS = "audioProgress"
    const val AUDIO_BUFFER_PROGRESS = "audioBufferProgress"
    const val AUDIO_SIZE = "audioSize"
    const val AUDIO_SPEED = "audioSpeed"
    const val NOTIFY_MAIN = "notifyMain"
    const val WEB_SERVICE = "webService"
    const val UP_DOWNLOAD = "upDownload"
    const val SAVE_CONTENT = "saveContent"
    const val CHECK_SOURCE = "checkSource"
    const val CHECK_SOURCE_DONE = "checkSourceDone"
    const val TIP_COLOR = "tipColor"
    const val SOURCE_CHANGED = "sourceChanged"
    const val SEARCH_RESULT = "searchResult"
    const val UPDATE_READ_ACTION_BAR = "updateReadActionBar"
    const val UP_SEEK_BAR = "upSeekBar"
    const val READ_ALOUD_PLAY = "readAloudPlay"
    const val EXPORT_BOOK = "exportBook"
    const val UP_MANGA_CONFIG = "upMangaConfig"
    const val PLAY_MODE_CHANGED = "playModeChanged"
}
