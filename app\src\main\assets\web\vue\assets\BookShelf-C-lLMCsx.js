import{d as J,a2 as j,s as M,e as h,o as u,g as e,F as U,P as W,M as S,y as g,c as $,w as P,h as V,a3 as H,u as o,B as I,a4 as ee,O as te,a5 as se,a6 as oe,S as ae,f as C,N as ne,D as re,G as ie,C as le,x as L,n as b,a7 as ce}from"./vendor-B0XMvmrm.js";import{d as de,A as w,i as ue,_ as O,u as pe,a as he,v as ge,l as me,s as ve,p as fe,b as D}from"./index-DTncJNuO.js";import{u as _e}from"./loading-ByWmlKy3.js";const we={class:"books-wrapper"},Be={class:"wrapper"},ke=["onClick"],ye={class:"cover-img"},Ae=["src"],Se={class:"info"},Ie={class:"name"},Ce={class:"sub"},xe={class:"author"},Re={key:0,class:"tags"},Ee={key:1,class:"update-info"},Le={class:"size"},be={class:"date"},Me={key:0,class:"intro"},Pe={key:1,class:"dur-chapter"},Ve={class:"last-chapter"},ze=J({__name:"BookItems",props:{books:{},isSearch:{type:Boolean}},emits:["bookClick"],setup(z,{emit:l}){j(r=>({"2a51eeb0":o(y)}));const B=z,x=l,a=r=>x("bookClick",r),k=({bookUrl:r,coverUrl:i})=>i===void 0?w.getProxyCoverUrl(r):ue(i)?w.getProxyCoverUrl(i):i,R=r=>{const i=r.target;i.src=w.getProxyCoverUrl(i.src)},y=M(()=>B.isSearch?"space-between":"flex-start");return(r,i)=>{const m=H;return u(),h("div",we,[e("div",Be,[(u(!0),h(U,null,W(r.books,n=>{var c;return u(),h("div",{class:"book",key:n.bookUrl,onClick:f=>a(n)},[e("div",ye,[(u(),h("img",{class:"cover",src:k(n),key:n.coverUrl,onErrorOnce:R,alt:"",loading:"lazy"},null,40,Ae))]),e("div",Se,[e("div",Ie,g(n.name),1),e("div",Ce,[e("div",xe,g(n.author),1),r.isSearch?(u(),h("div",Re,[(u(!0),h(U,null,W((c=n.kind)==null?void 0:c.split(",").slice(0,2),f=>(u(),$(m,{key:f},{default:P(()=>[V(g(f),1)]),_:2},1024))),128))])):S("",!0),r.isSearch?S("",!0):(u(),h("div",Ee,[i[0]||(i[0]=e("div",{class:"dot"},"•",-1)),e("div",Le,"共"+g(n.totalChapterNum)+"章",1),i[1]||(i[1]=e("div",{class:"dot"},"•",-1)),e("div",be,g(o(de)(n.lastCheckTime)),1)]))]),r.isSearch?(u(),h("div",Me,g(n.intro),1)):S("",!0),r.isSearch?S("",!0):(u(),h("div",Pe," 已读："+g(n.durChapterTitle),1)),e("div",Ve,"最新："+g(n.latestChapterTitle),1)])],8,ke)}),128))])])}}}),Te=O(ze,[["__scopeId","data-v-0f5f0160"]]),Ne="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAECUlEQVRYR7WXTYhcRRDHq3pY9yKrYBQ8KBsjgvHgwRhiQBTjYZm4Xe8NusawhwS/o9GLoKhgBGPAgJd1NdGIXwtZTbRf9Rqzl6gHTVyDeIkIgnEOghAM6oKHzTJd0sO8Zaa338zb7NjwmJn++Ndv+lVVVyOsoM3Ozl69sLBAiHiDc26NUuoKv9w5d14p9aeI/DI4OMgjIyN/lJXFMhOttQ8BgBaR0TLzEXEGAKzW+lCv+V0BmLmGiLtF5M5eQrFxRPxaRCaI6LOi9YUAzPwGADxxMYYjayaJ6MkoZKyTmU8AwF19Mp7LfElEW0LNZTvAzIcBYFufjedy00T0QLt2B4AxZo9S6qX/yXhT1jn3cpqme3IbSwDM/DgAvNlu3Dm3Uyl1HAA2IOJ2EdleEu5Io9H4EBHPVCqVLSISRsMuInrLazUBpqamhoaGhr4TkRsDgLVpmtbzPmPMLQBwOwD4vvzxw8P5IyJztVrtVL4my7L1iPhTx7Yj/jw/P79pfHx8vgmQZdkLiPhK+O8GBgauqVarv5f819FpxpjLlVJ/hYMi8mKSJHubAMz8KwBcF1EYI6IjqwRIlFImonGWiNZhlmVVRDxWYGTVAMx8HwB8EtMXka1orT0gIo9GJrxNRLH+FW8IMx8EgEeW5QDEgx5gTkQ2Bk7yr9b60hVb6rKAmc8BwJWBne+x4P3XiWhtPwGstV9FzpSzHuBvALgsMHaaiDp2ZbUwWZZNIuKuQOcfD7AAAJeEcaq1Xr9ao+3rmdknnscCzQse4LdWEukYazQaa2q12vl+QTDztwCwOdCr+zA8iYi3RQwREdl+ADDz9QDwIwB0OLaInPJRcEhEHoyEyAmt9d39ALDW2lg1hYjv+lfgC4WJgkTxcJIkPcuqbpC+qgKATwvm7PYAGwDgdBeRZ4notYvZCWPMDqXUe13W3to8C6y10yJyv//u6zj/2R6ziPiRiBwt6xPMrBExFZEdRcYR8WOt9bb8MNoKAJ+3Jvtwed05d4dSKtz+c4h4VGsdrRWttZMici8AXFVix+4homNLBUmWZQcQMc/9x4mommXZ84i4t11MKbV5dHR06bxvH5uZmbnZOfdN6O0RmMNE1CxulgCstdeKyBcAcFPrVTyltZ4wxiSVSuXplkhda72zh9P1rClFZFOSJHMdAP5Hq3rxR6eH+IGIvIOuqFlr94nIc10WdRzxy6riAMJnr2nn3JlcME3TppMWNWvtfhF5pmB8WX0RvZgEEEtaYUUbM2KtfUdE/FUubNHipvBmZIxZp5TaDwBprlQGIHLqzSHiPq01x4B7Xk6Z2d8TfDwPlwFozfd1f90598Hi4uKrY2NjFwrzQVkP81nNi/byAWOMv8gOp2n6fhnt/wDqJrRWLmhIrwAAAABJRU5ErkJggg==",Ue={class:"navigation-wrapper"},We={class:"search-wrapper"},De={class:"bottom-wrapper"},Je={class:"recent-wrapper"},He={class:"reading-recent"},Oe={class:"setting-wrapper"},Ze={class:"setting-item"},Fe={class:"bottom-icons"},Ke={href:"https://github.com/gedoor/legado_web_bookshelf",target:"_blank"},Ye={class:"bottom-icon"},Qe=["src"],qe=J({__name:"BookShelf",setup(z){const l=pe(),B=M(()=>l.isNight),x=s=>{try{s!==void 0&&l.setConfig(s)}catch{b.info("阅读界面配置解析错误")}},a=I({name:"尚无阅读记录",author:"",bookUrl:"",chapterIndex:0,chapterPos:0,isSeachBook:!1}),k=I(),{showLoading:R,closeLoading:y,loadingWrapper:r,isLoading:i}=_e(k,"正在获取书籍信息"),m=ee([]),n=M(()=>l.shelf),c=I(""),f=I(!1);te(()=>{if(!(f.value&&c.value!="")){if(f.value=!1,m.value=[],c.value==""){m.value=n.value;return}m.value=n.value.filter(s=>s.name.includes(c.value)||s.author.includes(c.value))}});const T=()=>{c.value!=""&&(m.value=[],l.clearSearchBooks(),R(),f.value=!0,w.search(c.value,s=>{i&&y();try{l.setSearchBooks(s),m.value=l.searchBooks}catch(t){throw b.error("后端数据错误"),t}},()=>{y(),m.value.length==0&&b.info("搜索结果为空")}))},A=he(),{connectStatus:Z,connectType:F,newConnect:K}=se(A),Y=()=>{ce.prompt("请输入 后端地址 ( 如：http://127.0.0.1:9527 或者通过内网穿透的地址)","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputPlaceholder:me,inputValidator:s=>ge(s),inputErrorMessage:"输入的格式不对",beforeClose:(s,t,v)=>{if(s==="confirm"){A.setNewConnect(!0),t.confirmButtonLoading=!0,t.confirmButtonText="校验中……";const d=new URL(t.inputValue).toString();w.getReadConfig(d).then(function(p){A.setNewConnect(!1),x(p),t.confirmButtonLoading=!1,l.clearSearchBooks(),ve(...fe(d)),d===location.origin?localStorage.removeItem(D):localStorage.setItem(D,d),l.loadBookShelf(),v()}).catch(function(p){throw A.setNewConnect(!1),t.confirmButtonLoading=!1,t.confirmButtonText="确定",p})}else v()}})},Q=oe(),q=async s=>{const t="respondTime"in s;t&&await w.saveBook(s);const{bookUrl:v,name:d,author:p,durChapterIndex:_=0,durChapterPos:E=0}=s;N(v,d,p,_,E,t)},N=(s,t,v,d,p,_=!1,E=!1)=>{if(t!=="尚无阅读记录"){if(E&&n.value.every(X=>X.bookUrl!==s)){c.value=t,T();return}sessionStorage.setItem("bookUrl",s),sessionStorage.setItem("bookName",t),sessionStorage.setItem("bookAuthor",v),sessionStorage.setItem("chapterIndex",String(d)),sessionStorage.setItem("chapterPos",String(p)),sessionStorage.setItem("isSeachBook",String(_)),a.value={name:t,author:v,bookUrl:s,chapterIndex:d,chapterPos:p,isSeachBook:_},localStorage.setItem("readingRecent",JSON.stringify(a.value)),Q.push({path:"/chapter"})}},G=async()=>{await l.loadWebConfig(),await l.saveBookProgress(),await l.loadBookShelf()};return ae(()=>{const s=localStorage.getItem("readingRecent");s!=null&&(a.value=JSON.parse(s),typeof a.value.chapterIndex>"u"&&(a.value.chapterIndex=0)),r(G())}),(s,t)=>{const v=le,d=H,p=Te;return u(),h("div",{class:L({"index-wrapper":!0,night:o(B),day:!o(B)})},[e("div",Ue,[t[4]||(t[4]=e("div",{class:"navigation-title-wrapper"},[e("div",{class:"navigation-title"},"阅读"),e("div",{class:"navigation-sub-title"},"清风不识字，何故乱翻书")],-1)),e("div",We,[C(v,{placeholder:"搜索书籍，在线书籍自动加入书架",modelValue:o(c),"onUpdate:modelValue":t[0]||(t[0]=_=>ie(c)?c.value=_:null),class:"search-input","prefix-icon":o(re),onKeyup:ne(T,["enter"])},null,8,["modelValue","prefix-icon"])]),e("div",De,[e("div",Je,[t[2]||(t[2]=e("div",{class:"recent-title"},"最近阅读",-1)),e("div",He,[C(d,{type:o(a).name=="尚无阅读记录"?"warning":"primary",class:L(["recent-book",{"no-point":o(a).bookUrl==""}]),size:"large",onClick:t[1]||(t[1]=_=>N(o(a).bookUrl,o(a).name,o(a).author,o(a).chapterIndex,o(a).chapterPos,o(a).isSeachBook,!0))},{default:P(()=>[V(g(o(a).name),1)]),_:1},8,["type","class"])])]),e("div",Oe,[t[3]||(t[3]=e("div",{class:"setting-title"},"基本设定",-1)),e("div",Ze,[C(d,{type:o(F),size:"large",class:L(["setting-connect",{"no-point":o(K)}]),onClick:Y},{default:P(()=>[V(g(o(Z)),1)]),_:1},8,["type","class"])])])]),e("div",Fe,[e("a",Ke,[e("div",Ye,[e("img",{src:o(Ne),alt:""},null,8,Qe)])])])]),e("div",{class:"shelf-wrapper",ref_key:"shelfWrapper",ref:k},[C(p,{books:o(m),onBookClick:q,isSearch:o(f)},null,8,["books","isSearch"])],512)],2)}}}),$e=O(qe,[["__scopeId","data-v-5061e9c0"]]);export{$e as default};
