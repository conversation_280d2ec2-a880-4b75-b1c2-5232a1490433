{"formatVersion": 1, "database": {"version": 62, "identityHash": "965c5f1d4c60d25b4b46efb17c575c85", "entities": [{"tableName": "books", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bookUrl` TEXT NOT NULL DEFAULT '', `tocUrl` TEXT NOT NULL DEFAULT '', `origin` TEXT NOT NULL DEFAULT 'loc_book', `originName` TEXT NOT NULL DEFAULT '', `name` TEXT NOT NULL DEFAULT '', `author` TEXT NOT NULL DEFAULT '', `kind` TEXT, `customTag` TEXT, `coverUrl` TEXT, `customCoverUrl` TEXT, `intro` TEXT, `customIntro` TEXT, `charset` TEXT, `type` INTEGER NOT NULL DEFAULT 0, `group` INTEGER NOT NULL DEFAULT 0, `latestChapterTitle` TEXT, `latestChapterTime` INTEGER NOT NULL DEFAULT 0, `lastCheckTime` INTEGER NOT NULL DEFAULT 0, `lastCheckCount` INTEGER NOT NULL DEFAULT 0, `totalChapterNum` INTEGER NOT NULL DEFAULT 0, `durChapterTitle` TEXT, `durChapterIndex` INTEGER NOT NULL DEFAULT 0, `durChapterPos` INTEGER NOT NULL DEFAULT 0, `durChapterTime` INTEGER NOT NULL DEFAULT 0, `wordCount` TEXT, `canUpdate` INTEGER NOT NULL DEFAULT 1, `order` INTEGER NOT NULL DEFAULT 0, `originOrder` INTEGER NOT NULL DEFAULT 0, `variable` TEXT, `readConfig` TEXT, PRIMARY KEY(`bookUrl`))", "fields": [{"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "tocUrl", "columnName": "tocUrl", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true, "defaultValue": "'loc_book'"}, {"fieldPath": "originName", "columnName": "originName", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "author", "columnName": "author", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "kind", "columnName": "kind", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customTag", "columnName": "customTag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverUrl", "columnName": "coverUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customCoverUrl", "columnName": "customCoverUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "intro", "columnName": "intro", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customIntro", "columnName": "customIntro", "affinity": "TEXT", "notNull": false}, {"fieldPath": "charset", "columnName": "charset", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "group", "columnName": "group", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "latestChapterTitle", "columnName": "latestChapterTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "latestChapterTime", "columnName": "latestChapterTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "lastCheckTime", "columnName": "lastCheckTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "lastCheckCount", "columnName": "lastCheckCount", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "totalChapterNum", "columnName": "totalChapterNum", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "dur<PERSON>hapterTitle", "columnName": "dur<PERSON>hapterTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "durChapterIndex", "columnName": "durChapterIndex", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "durChapterPos", "columnName": "durChapterPos", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "durChapterTime", "columnName": "durChapterTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "wordCount", "columnName": "wordCount", "affinity": "TEXT", "notNull": false}, {"fieldPath": "canUpdate", "columnName": "canUpdate", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "originOrder", "columnName": "originOrder", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}, {"fieldPath": "readConfig", "columnName": "readConfig", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["bookUrl"]}, "indices": [{"name": "index_books_name_author", "unique": true, "columnNames": ["name", "author"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_books_name_author` ON `${TABLE_NAME}` (`name`, `author`)"}], "foreignKeys": []}, {"tableName": "book_groups", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`groupId` INTEGER NOT NULL, `groupName` TEXT NOT NULL, `cover` TEXT, `order` INTEGER NOT NULL, `show` INTEGER NOT NULL, `bookSort` INTEGER NOT NULL DEFAULT -1, PRIMARY KEY(`groupId`))", "fields": [{"fieldPath": "groupId", "columnName": "groupId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "groupName", "columnName": "groupName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "cover", "columnName": "cover", "affinity": "TEXT", "notNull": false}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "show", "columnName": "show", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookSort", "columnName": "bookSort", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}], "primaryKey": {"autoGenerate": false, "columnNames": ["groupId"]}, "indices": [], "foreignKeys": []}, {"tableName": "book_sources", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bookSourceUrl` TEXT NOT NULL, `bookSourceName` TEXT NOT NULL, `bookSourceGroup` TEXT, `bookSourceType` INTEGER NOT NULL, `bookUrlPattern` TEXT, `customOrder` INTEGER NOT NULL, `enabled` INTEGER NOT NULL, `enabledExplore` INTEGER NOT NULL, `enabledReview` INTEGER, `enabledCookieJar` INTEGER DEFAULT 0, `concurrentRate` TEXT, `header` TEXT, `loginUrl` TEXT, `loginUi` TEXT, `loginCheckJs` TEXT, `coverDecodeJs` TEXT, `bookSourceComment` TEXT, `variableComment` TEXT, `lastUpdateTime` INTEGER NOT NULL, `respondTime` INTEGER NOT NULL, `weight` INTEGER NOT NULL, `exploreUrl` TEXT, `ruleExplore` TEXT, `searchUrl` TEXT, `ruleSearch` TEXT, `ruleBookInfo` TEXT, `ruleToc` TEXT, `ruleContent` TEXT, `ruleReview` TEXT, PRIMARY KEY(`bookSourceUrl`))", "fields": [{"fieldPath": "bookSourceUrl", "columnName": "bookSourceUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookSourceName", "columnName": "bookSourceName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookSourceGroup", "columnName": "bookSourceGroup", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bookSourceType", "columnName": "bookSourceType", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookUrlPattern", "columnName": "bookUrlPattern", "affinity": "TEXT", "notNull": false}, {"fieldPath": "customOrder", "columnName": "customOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enabled", "columnName": "enabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enabledExplore", "columnName": "enabledExplore", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enabledReview", "columnName": "enabledReview", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "enabledCookieJar", "columnName": "enabledCookieJar", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "concurrentRate", "columnName": "concurrentRate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "header", "columnName": "header", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginUrl", "columnName": "loginUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginUi", "columnName": "loginUi", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginCheckJs", "columnName": "loginCheckJs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverDecodeJs", "columnName": "coverDecodeJs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bookSourceComment", "columnName": "bookSourceComment", "affinity": "TEXT", "notNull": false}, {"fieldPath": "variableComment", "columnName": "variableComment", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "respondTime", "columnName": "respondTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weight", "columnName": "weight", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "exploreUrl", "columnName": "exploreUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleExplore", "columnName": "ruleExplore", "affinity": "TEXT", "notNull": false}, {"fieldPath": "searchUrl", "columnName": "searchUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleSearch", "columnName": "ruleSearch", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleBookInfo", "columnName": "ruleBookInfo", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleToc", "columnName": "ruleToc", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleContent", "columnName": "ruleContent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleReview", "columnName": "ruleReview", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["bookSourceUrl"]}, "indices": [{"name": "index_book_sources_bookSourceUrl", "unique": false, "columnNames": ["bookSourceUrl"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_book_sources_bookSourceUrl` ON `${TABLE_NAME}` (`bookSourceUrl`)"}], "foreignKeys": []}, {"tableName": "chapters", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`url` TEXT NOT NULL, `title` TEXT NOT NULL, `isVolume` INTEGER NOT NULL, `baseUrl` TEXT NOT NULL, `bookUrl` TEXT NOT NULL, `index` INTEGER NOT NULL, `isVip` INTEGER NOT NULL, `isPay` INTEGER NOT NULL, `resourceUrl` TEXT, `tag` TEXT, `start` INTEGER, `end` INTEGER, `startFragmentId` TEXT, `endFragmentId` TEXT, `variable` TEXT, PRIMARY KEY(`url`, `bookUrl`), FOREIGN KEY(`bookUrl`) REFERENCES `books`(`bookUrl`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isVolume", "columnName": "isVolume", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "baseUrl", "columnName": "baseUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "index", "columnName": "index", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "isVip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPay", "columnName": "isPay", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "resourceUrl", "columnName": "resourceUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": false}, {"fieldPath": "start", "columnName": "start", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "end", "columnName": "end", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "startFragmentId", "columnName": "startFragmentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "endFragmentId", "columnName": "endFragmentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["url", "bookUrl"]}, "indices": [{"name": "index_chapters_bookUrl", "unique": false, "columnNames": ["bookUrl"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_chapters_bookUrl` ON `${TABLE_NAME}` (`bookUrl`)"}, {"name": "index_chapters_bookUrl_index", "unique": true, "columnNames": ["bookUrl", "index"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_chapters_bookUrl_index` ON `${TABLE_NAME}` (`bookUrl`, `index`)"}], "foreignKeys": [{"table": "books", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["bookUrl"], "referencedColumns": ["bookUrl"]}]}, {"tableName": "replace_rules", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL DEFAULT '', `group` TEXT, `pattern` TEXT NOT NULL DEFAULT '', `replacement` TEXT NOT NULL DEFAULT '', `scope` TEXT, `scopeTitle` INTEGER NOT NULL DEFAULT 0, `scopeContent` INTEGER NOT NULL DEFAULT 1, `isEnabled` INTEGER NOT NULL DEFAULT 1, `isRegex` INTEGER NOT NULL DEFAULT 1, `timeoutMillisecond` INTEGER NOT NULL DEFAULT 3000, `sortOrder` INTEGER NOT NULL DEFAULT 0)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "group", "columnName": "group", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pattern", "columnName": "pattern", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "replacement", "columnName": "replacement", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "scope", "columnName": "scope", "affinity": "TEXT", "notNull": false}, {"fieldPath": "scopeTitle", "columnName": "scopeTitle", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "scopeContent", "columnName": "scopeContent", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "isEnabled", "columnName": "isEnabled", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "isRegex", "columnName": "isRegex", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "timeoutMillisecond", "columnName": "timeoutMillisecond", "affinity": "INTEGER", "notNull": true, "defaultValue": "3000"}, {"fieldPath": "order", "columnName": "sortOrder", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_replace_rules_id", "unique": false, "columnNames": ["id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_replace_rules_id` ON `${TABLE_NAME}` (`id`)"}], "foreignKeys": []}, {"tableName": "searchBooks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`bookUrl` TEXT NOT NULL, `origin` TEXT NOT NULL, `originName` TEXT NOT NULL, `type` INTEGER NOT NULL, `name` TEXT NOT NULL, `author` TEXT NOT NULL, `kind` TEXT, `coverUrl` TEXT, `intro` TEXT, `wordCount` TEXT, `latestChapterTitle` TEXT, `tocUrl` TEXT NOT NULL, `time` INTEGER NOT NULL, `variable` TEXT, `originOrder` INTEGER NOT NULL, PRIMARY KEY(`bookUrl`), FOREIGN KEY(`origin`) REFERENCES `book_sources`(`bookSourceUrl`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "bookUrl", "columnName": "bookUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "originName", "columnName": "originName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "author", "columnName": "author", "affinity": "TEXT", "notNull": true}, {"fieldPath": "kind", "columnName": "kind", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverUrl", "columnName": "coverUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "intro", "columnName": "intro", "affinity": "TEXT", "notNull": false}, {"fieldPath": "wordCount", "columnName": "wordCount", "affinity": "TEXT", "notNull": false}, {"fieldPath": "latestChapterTitle", "columnName": "latestChapterTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "tocUrl", "columnName": "tocUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}, {"fieldPath": "originOrder", "columnName": "originOrder", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["bookUrl"]}, "indices": [{"name": "index_searchBooks_bookUrl", "unique": true, "columnNames": ["bookUrl"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_searchBooks_bookUrl` ON `${TABLE_NAME}` (`bookUrl`)"}, {"name": "index_searchBooks_origin", "unique": false, "columnNames": ["origin"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_searchBooks_origin` ON `${TABLE_NAME}` (`origin`)"}], "foreignKeys": [{"table": "book_sources", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["origin"], "referencedColumns": ["bookSourceUrl"]}]}, {"tableName": "search_keywords", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`word` TEXT NOT NULL, `usage` INTEGER NOT NULL, `lastUseTime` INTEGER NOT NULL, PRIMARY KEY(`word`))", "fields": [{"fieldPath": "word", "columnName": "word", "affinity": "TEXT", "notNull": true}, {"fieldPath": "usage", "columnName": "usage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastUseTime", "columnName": "lastUseTime", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["word"]}, "indices": [{"name": "index_search_keywords_word", "unique": true, "columnNames": ["word"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_search_keywords_word` ON `${TABLE_NAME}` (`word`)"}], "foreignKeys": []}, {"tableName": "cookies", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`url` TEXT NOT NULL, `cookie` TEXT NOT NULL, PRIMARY KEY(`url`))", "fields": [{"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "cookie", "columnName": "cookie", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["url"]}, "indices": [{"name": "index_cookies_url", "unique": true, "columnNames": ["url"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_cookies_url` ON `${TABLE_NAME}` (`url`)"}], "foreignKeys": []}, {"tableName": "rssSources", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sourceUrl` TEXT NOT NULL, `sourceName` TEXT NOT NULL, `sourceIcon` TEXT NOT NULL, `sourceGroup` TEXT, `sourceComment` TEXT, `enabled` INTEGER NOT NULL, `variableComment` TEXT, `enabledCookieJar` INTEGER DEFAULT 0, `concurrentRate` TEXT, `header` TEXT, `loginUrl` TEXT, `loginUi` TEXT, `loginCheckJs` TEXT, `coverDecodeJs` TEXT, `sortUrl` TEXT, `singleUrl` INTEGER NOT NULL, `articleStyle` INTEGER NOT NULL DEFAULT 0, `ruleArticles` TEXT, `ruleNextPage` TEXT, `ruleTitle` TEXT, `rulePubDate` TEXT, `ruleDescription` TEXT, `ruleImage` TEXT, `ruleLink` TEXT, `ruleContent` TEXT, `contentWhitelist` TEXT, `contentBlacklist` TEXT, `style` TEXT, `enableJs` INTEGER NOT NULL DEFAULT 1, `loadWithBaseUrl` INTEGER NOT NULL DEFAULT 1, `injectJs` TEXT, `lastUpdateTime` INTEGER NOT NULL DEFAULT 0, `customOrder` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`sourceUrl`))", "fields": [{"fieldPath": "sourceUrl", "columnName": "sourceUrl", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceName", "columnName": "sourceName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceIcon", "columnName": "sourceIcon", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sourceGroup", "columnName": "sourceGroup", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sourceComment", "columnName": "sourceComment", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enabled", "columnName": "enabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "variableComment", "columnName": "variableComment", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enabledCookieJar", "columnName": "enabledCookieJar", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "concurrentRate", "columnName": "concurrentRate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "header", "columnName": "header", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginUrl", "columnName": "loginUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginUi", "columnName": "loginUi", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginCheckJs", "columnName": "loginCheckJs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "coverDecodeJs", "columnName": "coverDecodeJs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sortUrl", "columnName": "sortUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "singleUrl", "columnName": "singleUrl", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "articleStyle", "columnName": "articleStyle", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "ruleArticles", "columnName": "ruleArticles", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleNextPage", "columnName": "ruleNextPage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleTitle", "columnName": "ruleTitle", "affinity": "TEXT", "notNull": false}, {"fieldPath": "rulePubDate", "columnName": "rulePubDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleDescription", "columnName": "ruleDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleImage", "columnName": "ruleImage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleLink", "columnName": "ruleLink", "affinity": "TEXT", "notNull": false}, {"fieldPath": "ruleContent", "columnName": "ruleContent", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "contentBlacklist", "columnName": "contentBlacklist", "affinity": "TEXT", "notNull": false}, {"fieldPath": "style", "columnName": "style", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enableJs", "columnName": "enableJs", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "loadWithBaseUrl", "columnName": "loadWithBaseUrl", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "injectJs", "columnName": "injectJs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "customOrder", "columnName": "customOrder", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["sourceUrl"]}, "indices": [{"name": "index_rssSources_sourceUrl", "unique": false, "columnNames": ["sourceUrl"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_rssSources_sourceUrl` ON `${TABLE_NAME}` (`sourceUrl`)"}], "foreignKeys": []}, {"tableName": "bookmarks", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`time` INTEGER NOT NULL, `bookName` TEXT NOT NULL, `bookAuthor` TEXT NOT NULL, `chapterIndex` INTEGER NOT NULL, `chapterPos` INTEGER NOT NULL, `chapterName` TEXT NOT NULL, `bookText` TEXT NOT NULL, `content` TEXT NOT NULL, PRIMARY KEY(`time`))", "fields": [{"fieldPath": "time", "columnName": "time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bookName", "columnName": "bookName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "book<PERSON><PERSON><PERSON>", "columnName": "book<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chapterIndex", "columnName": "chapterIndex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chapterPos", "columnName": "chapterPos", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "chapterName", "columnName": "chapterName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookText", "columnName": "bookText", "affinity": "TEXT", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["time"]}, "indices": [{"name": "index_bookmarks_bookName_bookAuthor", "unique": false, "columnNames": ["bookName", "book<PERSON><PERSON><PERSON>"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_bookmarks_bookName_bookAuthor` ON `${TABLE_NAME}` (`bookName`, `bookAuthor`)"}], "foreignKeys": []}, {"tableName": "rssArticles", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`origin` TEXT NOT NULL, `sort` TEXT NOT NULL, `title` TEXT NOT NULL, `order` INTEGER NOT NULL, `link` TEXT NOT NULL, `pubDate` TEXT, `description` TEXT, `content` TEXT, `image` TEXT, `read` INTEGER NOT NULL, `variable` TEXT, PRIMARY KEY(`origin`, `link`))", "fields": [{"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "order", "columnName": "order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "link", "columnName": "link", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pubDate", "columnName": "pubDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": false}, {"fieldPath": "read", "columnName": "read", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["origin", "link"]}, "indices": [], "foreignKeys": []}, {"tableName": "rssReadRecords", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`record` TEXT NOT NULL, `read` INTEGER NOT NULL, PRIMARY KEY(`record`))", "fields": [{"fieldPath": "record", "columnName": "record", "affinity": "TEXT", "notNull": true}, {"fieldPath": "read", "columnName": "read", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["record"]}, "indices": [], "foreignKeys": []}, {"tableName": "rssStars", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`origin` TEXT NOT NULL, `sort` TEXT NOT NULL, `title` TEXT NOT NULL, `starTime` INTEGER NOT NULL, `link` TEXT NOT NULL, `pubDate` TEXT, `description` TEXT, `content` TEXT, `image` TEXT, `variable` TEXT, PRIMARY KEY(`origin`, `link`))", "fields": [{"fieldPath": "origin", "columnName": "origin", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sort", "columnName": "sort", "affinity": "TEXT", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "starTime", "columnName": "starTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "link", "columnName": "link", "affinity": "TEXT", "notNull": true}, {"fieldPath": "pubDate", "columnName": "pubDate", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": false}, {"fieldPath": "image", "columnName": "image", "affinity": "TEXT", "notNull": false}, {"fieldPath": "variable", "columnName": "variable", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["origin", "link"]}, "indices": [], "foreignKeys": []}, {"tableName": "txtTocRules", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `rule` TEXT NOT NULL, `example` TEXT, `serialNumber` INTEGER NOT NULL, `enable` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "rule", "columnName": "rule", "affinity": "TEXT", "notNull": true}, {"fieldPath": "example", "columnName": "example", "affinity": "TEXT", "notNull": false}, {"fieldPath": "serialNumber", "columnName": "serialNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "enable", "columnName": "enable", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "readRecord", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`deviceId` TEXT NOT NULL, `bookName` TEXT NOT NULL, `readTime` INTEGER NOT NULL DEFAULT 0, `lastRead` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`deviceId`, `bookName`))", "fields": [{"fieldPath": "deviceId", "columnName": "deviceId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bookName", "columnName": "bookName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "readTime", "columnName": "readTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "lastRead", "columnName": "lastRead", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["deviceId", "bookName"]}, "indices": [], "foreignKeys": []}, {"tableName": "httpTTS", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `url` TEXT NOT NULL, `contentType` TEXT, `concurrentRate` TEXT DEFAULT '0', `loginUrl` TEXT, `loginUi` TEXT, `header` TEXT, `enabledCookieJar` INTEGER DEFAULT 0, `loginCheckJs` TEXT, `lastUpdateTime` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "contentType", "columnName": "contentType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "concurrentRate", "columnName": "concurrentRate", "affinity": "TEXT", "notNull": false, "defaultValue": "'0'"}, {"fieldPath": "loginUrl", "columnName": "loginUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "loginUi", "columnName": "loginUi", "affinity": "TEXT", "notNull": false}, {"fieldPath": "header", "columnName": "header", "affinity": "TEXT", "notNull": false}, {"fieldPath": "enabledCookieJar", "columnName": "enabledCookieJar", "affinity": "INTEGER", "notNull": false, "defaultValue": "0"}, {"fieldPath": "loginCheckJs", "columnName": "loginCheckJs", "affinity": "TEXT", "notNull": false}, {"fieldPath": "lastUpdateTime", "columnName": "lastUpdateTime", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "caches", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `value` TEXT, `deadline` INTEGER NOT NULL, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": false}, {"fieldPath": "deadline", "columnName": "deadline", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["key"]}, "indices": [{"name": "index_caches_key", "unique": true, "columnNames": ["key"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_caches_key` ON `${TABLE_NAME}` (`key`)"}], "foreignKeys": []}, {"tableName": "ruleSubs", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `url` TEXT NOT NULL, `type` INTEGER NOT NULL, `customOrder` INTEGER NOT NULL, `autoUpdate` INTEGER NOT NULL, `update` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "customOrder", "columnName": "customOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "autoUpdate", "columnName": "autoUpdate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "update", "columnName": "update", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "dictRules", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `urlRule` TEXT NOT NULL, `showRule` TEXT NOT NULL, `enabled` INTEGER NOT NULL DEFAULT 1, `sortNumber` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`name`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "urlRule", "columnName": "urlRule", "affinity": "TEXT", "notNull": true}, {"fieldPath": "showRule", "columnName": "showRule", "affinity": "TEXT", "notNull": true}, {"fieldPath": "enabled", "columnName": "enabled", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "sortNumber", "columnName": "sortNumber", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["name"]}, "indices": [], "foreignKeys": []}, {"tableName": "keyboardAssists", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`type` INTEGER NOT NULL DEFAULT 0, `key` TEXT NOT NULL DEFAULT '', `value` TEXT NOT NULL DEFAULT '', `serialNo` INTEGER NOT NULL DEFAULT 0, PRIMARY KEY(`type`, `key`))", "fields": [{"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "value", "columnName": "value", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "serialNo", "columnName": "serialNo", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": false, "columnNames": ["type", "key"]}, "indices": [], "foreignKeys": []}, {"tableName": "servers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `type` TEXT NOT NULL, `config` TEXT, `sortNumber` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "config", "columnName": "config", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sortNumber", "columnName": "sortNumber", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '965c5f1d4c60d25b4b46efb17c575c85')"]}}